import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Switch,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SoundOutlined,
  ExclamationCircleOutlined,
  ToolOutlined
} from '@ant-design/icons';
import { announcementApi } from '@/services/api';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;
const { Title } = Typography;

interface Announcement {
  _id: string;
  title: string;
  content: string;
  type: 'system' | 'notice' | 'urgent';
  priority: number;
  status: 'active' | 'inactive' | 'expired';
  publishTime: string;
  effectiveTime: string;
  expireTime: string;
  isTop: boolean;
  viewCount: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

const AnnouncementManagement: React.FC = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<Announcement | null>(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 统计数据
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    expired: 0,
    totalViews: 0
  });

  useEffect(() => {
    loadAnnouncements();
  }, [pagination.current, pagination.pageSize]);

  // 加载公告列表
  const loadAnnouncements = async () => {
    try {
      setLoading(true);
      const response = await announcementApi.getList({
        page: pagination.current,
        pageSize: pagination.pageSize,
        isAdmin: true
      });

      if (response.success) {
        setAnnouncements(response.data.list);
        setPagination(prev => ({
          ...prev,
          total: response.data.total
        }));

        // 计算统计数据
        const stats = response.data.list.reduce((acc: any, item: Announcement) => {
          acc.total++;
          if (item.status === 'active') acc.active++;
          if (item.status === 'expired') acc.expired++;
          acc.totalViews += item.viewCount || 0;
          return acc;
        }, { total: 0, active: 0, expired: 0, totalViews: 0 });

        setStatistics(stats);
      }
    } catch (error) {
      message.error('加载公告列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开新增/编辑弹窗
  const openModal = (announcement?: Announcement) => {
    setEditingAnnouncement(announcement || null);
    setModalVisible(true);
    
    if (announcement) {
      form.setFieldsValue({
        ...announcement,
        publishTime: dayjs(announcement.publishTime),
        effectiveTime: dayjs(announcement.effectiveTime),
        expireTime: dayjs(announcement.expireTime)
      });
    } else {
      form.resetFields();
      // 设置默认值
      const now = dayjs();
      form.setFieldsValue({
        type: 'notice',
        priority: 2,
        status: 'active',
        publishTime: now,
        effectiveTime: now,
        expireTime: now.add(30, 'day'),
        isTop: false
      });
    }
  };

  // 关闭弹窗
  const closeModal = () => {
    setModalVisible(false);
    setEditingAnnouncement(null);
    form.resetFields();
  };

  // 保存公告
  const saveAnnouncement = async () => {
    try {
      const values = await form.validateFields();
      
      const data = {
        ...values,
        publishTime: values.publishTime.toISOString(),
        effectiveTime: values.effectiveTime.toISOString(),
        expireTime: values.expireTime.toISOString()
      };

      if (editingAnnouncement) {
        await announcementApi.update(editingAnnouncement._id, data);
        message.success('公告更新成功');
      } else {
        await announcementApi.create(data);
        message.success('公告创建成功');
      }

      closeModal();
      loadAnnouncements();
    } catch (error) {
      message.error('保存公告失败');
    }
  };

  // 删除公告
  const deleteAnnouncement = async (id: string) => {
    try {
      await announcementApi.delete(id);
      message.success('公告删除成功');
      loadAnnouncements();
    } catch (error) {
      message.error('删除公告失败');
    }
  };

  // 批量更新状态
  const batchUpdateStatus = async (ids: string[], status: string) => {
    try {
      await announcementApi.batchUpdate(ids, status);
      message.success('批量更新成功');
      loadAnnouncements();
    } catch (error) {
      message.error('批量更新失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<Announcement> = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
      render: (text, record) => (
        <Space>
          {record.isTop && <Tag color="red">置顶</Tag>}
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => {
        const config = {
          system: { color: 'blue', icon: <ToolOutlined />, text: '系统' },
          urgent: { color: 'red', icon: <ExclamationCircleOutlined />, text: '紧急' },
          notice: { color: 'orange', icon: <SoundOutlined />, text: '通知' }
        };
        const { color, icon, text } = config[type] || config.notice;
        return <Tag color={color} icon={icon}>{text}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const config = {
          active: { color: 'green', text: '激活' },
          inactive: { color: 'default', text: '停用' },
          expired: { color: 'red', text: '过期' }
        };
        const { color, text } = config[status] || config.inactive;
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority) => {
        const config = {
          1: { color: 'red', text: '高' },
          2: { color: 'orange', text: '中' },
          3: { color: 'default', text: '低' }
        };
        const { color, text } = config[priority] || config[2];
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '查看次数',
      dataIndex: 'viewCount',
      key: 'viewCount',
      width: 100,
      render: (count) => count || 0
    },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      key: 'publishTime',
      width: 150,
      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '有效期至',
      dataIndex: 'expireTime',
      key: 'expireTime',
      width: 150,
      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {/* 查看详情 */}}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条公告吗？"
            onConfirm={() => deleteAnnouncement(record._id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>公告管理</Title>
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic title="总公告数" value={statistics.total} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="激活中" value={statistics.active} valueStyle={{ color: '#3f8600' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="已过期" value={statistics.expired} valueStyle={{ color: '#cf1322' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总查看次数" value={statistics.totalViews} />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openModal()}
          >
            新增公告
          </Button>
          <Button onClick={loadAnnouncements}>刷新</Button>
        </Space>
      </Card>

      {/* 公告列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={announcements}
          rowKey="_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({ ...prev, current: page, pageSize }));
            }
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑弹窗 */}
      <Modal
        title={editingAnnouncement ? '编辑公告' : '新增公告'}
        open={modalVisible}
        onOk={saveAnnouncement}
        onCancel={closeModal}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'notice',
            priority: 2,
            status: 'active',
            isTop: false
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label="公告标题"
                rules={[{ required: true, message: '请输入公告标题' }]}
              >
                <Input placeholder="请输入公告标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="公告类型"
                rules={[{ required: true, message: '请选择公告类型' }]}
              >
                <Select>
                  <Option value="notice">普通通知</Option>
                  <Option value="system">系统公告</Option>
                  <Option value="urgent">紧急通知</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="content"
            label="公告内容"
            rules={[{ required: true, message: '请输入公告内容' }]}
          >
            <TextArea rows={6} placeholder="请输入公告内容" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select>
                  <Option value={1}>高</Option>
                  <Option value={2}>中</Option>
                  <Option value={3}>低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="active">激活</Option>
                  <Option value="inactive">停用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="isTop" label="是否置顶" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="publishTime"
                label="发布时间"
                rules={[{ required: true, message: '请选择发布时间' }]}
              >
                <DatePicker showTime style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="effectiveTime"
                label="生效时间"
                rules={[{ required: true, message: '请选择生效时间' }]}
              >
                <DatePicker showTime style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="expireTime"
                label="过期时间"
                rules={[{ required: true, message: '请选择过期时间' }]}
              >
                <DatePicker showTime style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default AnnouncementManagement;
