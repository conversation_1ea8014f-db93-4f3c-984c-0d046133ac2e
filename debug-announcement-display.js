// 调试公告显示问题的脚本
// 请在微信开发者工具的控制台中运行此代码

console.log('🔍 开始调试公告显示问题...');

// 获取当前页面实例
function getCurrentPageInstance() {
  const pages = getCurrentPages();
  return pages[pages.length - 1];
}

// 检查首页数据
function checkIndexPageData() {
  try {
    const currentPage = getCurrentPageInstance();
    console.log('📄 当前页面路径:', currentPage.route);
    
    if (currentPage.route === 'pages/index/index') {
      console.log('✅ 当前在首页');
      
      // 检查公告数据
      const announcements = currentPage.data.announcements;
      console.log('📊 首页公告数据:', announcements);
      console.log('📊 公告数量:', announcements ? announcements.length : 0);
      
      if (announcements && announcements.length > 0) {
        console.log('✅ 首页有公告数据');
        announcements.forEach((item, index) => {
          console.log(`📄 公告 ${index + 1}:`, {
            id: item._id,
            title: item.title,
            type: item.type,
            status: item.status
          });
        });
      } else {
        console.log('❌ 首页没有公告数据');
      }
      
      // 检查其他相关数据
      console.log('📊 其他页面数据:');
      console.log('  - themeLoaded:', currentPage.data.themeLoaded);
      console.log('  - loading:', currentPage.data.loading);
      
    } else {
      console.log('⚠️ 当前不在首页，请切换到首页后重试');
    }
    
  } catch (error) {
    console.error('❌ 检查页面数据异常:', error);
  }
}

// 手动加载公告数据
async function manualLoadAnnouncements() {
  try {
    console.log('🔄 手动加载公告数据...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此函数');
      return;
    }
    
    // 调用首页的加载公告方法
    if (typeof currentPage.loadAnnouncements === 'function') {
      console.log('📞 调用首页的 loadAnnouncements 方法...');
      await currentPage.loadAnnouncements();
      
      // 检查加载后的数据
      setTimeout(() => {
        const announcements = currentPage.data.announcements;
        console.log('📊 加载后的公告数据:', announcements);
        console.log('📊 公告数量:', announcements ? announcements.length : 0);
      }, 1000);
      
    } else {
      console.log('❌ 首页没有 loadAnnouncements 方法');
    }
    
  } catch (error) {
    console.error('❌ 手动加载公告异常:', error);
  }
}

// 直接设置公告数据测试
async function setTestAnnouncementData() {
  try {
    console.log('🧪 设置测试公告数据...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此函数');
      return;
    }
    
    // 设置测试数据
    const testAnnouncements = [
      {
        _id: 'test-1',
        title: '🎉 测试公告标题',
        content: '这是一个测试公告内容，用于验证公告横幅是否能正常显示。',
        type: 'notice',
        status: 'active',
        isTop: true,
        publishTime: new Date().toISOString()
      }
    ];
    
    currentPage.setData({
      announcements: testAnnouncements
    });
    
    console.log('✅ 测试公告数据已设置');
    console.log('📊 设置的数据:', testAnnouncements);
    
    // 检查设置后的数据
    setTimeout(() => {
      const announcements = currentPage.data.announcements;
      console.log('📊 设置后的公告数据:', announcements);
    }, 500);
    
  } catch (error) {
    console.error('❌ 设置测试数据异常:', error);
  }
}

// 检查组件是否正确渲染
function checkComponentRendering() {
  try {
    console.log('🔍 检查组件渲染...');
    
    // 查找公告横幅元素
    const query = wx.createSelectorQuery();
    query.select('.announcement-banner').boundingClientRect();
    query.exec((res) => {
      console.log('📊 公告横幅元素查询结果:', res);
      
      if (res[0]) {
        console.log('✅ 找到公告横幅元素');
        console.log('📐 元素位置和大小:', res[0]);
      } else {
        console.log('❌ 没有找到公告横幅元素');
      }
    });
    
  } catch (error) {
    console.error('❌ 检查组件渲染异常:', error);
  }
}

// 强制刷新页面
function forceRefreshPage() {
  try {
    console.log('🔄 强制刷新页面...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此函数');
      return;
    }
    
    // 重新调用 onLoad
    if (typeof currentPage.onLoad === 'function') {
      console.log('📞 重新调用 onLoad...');
      currentPage.onLoad();
    }
    
  } catch (error) {
    console.error('❌ 强制刷新异常:', error);
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.checkIndexPageData = checkIndexPageData;
  window.manualLoadAnnouncements = manualLoadAnnouncements;
  window.setTestAnnouncementData = setTestAnnouncementData;
  window.checkComponentRendering = checkComponentRendering;
  window.forceRefreshPage = forceRefreshPage;
}

console.log('📋 调试脚本加载完成！');
console.log('💡 使用方法：');
console.log('  - 检查首页数据: checkIndexPageData()');
console.log('  - 手动加载公告: manualLoadAnnouncements()');
console.log('  - 设置测试数据: setTestAnnouncementData()');
console.log('  - 检查组件渲染: checkComponentRendering()');
console.log('  - 强制刷新页面: forceRefreshPage()');

console.log('🔄 自动开始检查...');
checkIndexPageData();
