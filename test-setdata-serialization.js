// 测试微信小程序setData序列化问题的脚本
// 在微信开发者工具的控制台中运行此脚本

console.log('🧪 开始测试setData序列化问题...');

// 1. 模拟包含Date对象的订单数据
const mockOrderWithDate = {
  _id: "test_order_" + Date.now(),
  title: "测试订单",
  createTime: new Date(), // Date对象
  updateTime: new Date(), // Date对象
  status: "pending"
};

console.log('📦 原始订单数据:', mockOrderWithDate);
console.log('📦 createTime类型:', typeof mockOrderWithDate.createTime);
console.log('📦 createTime是否为Date:', mockOrderWithDate.createTime instanceof Date);

// 2. 模拟setData序列化过程
function simulateSetDataSerialization(data) {
  // 微信小程序的setData会将数据序列化为JSON，然后再反序列化
  const serialized = JSON.stringify(data);
  const deserialized = JSON.parse(serialized);
  return deserialized;
}

const serializedOrder = simulateSetDataSerialization(mockOrderWithDate);
console.log('\n🔄 setData序列化后的数据:', serializedOrder);
console.log('🔄 序列化后createTime类型:', typeof serializedOrder.createTime);
console.log('🔄 序列化后createTime是否为Date:', serializedOrder.createTime instanceof Date);
console.log('🔄 序列化后createTime值:', serializedOrder.createTime);

// 3. 测试修复方案：转换为ISO字符串
const fixedOrder = {
  ...mockOrderWithDate,
  createTime: mockOrderWithDate.createTime instanceof Date ? 
    mockOrderWithDate.createTime.toISOString() : 
    mockOrderWithDate.createTime,
  updateTime: mockOrderWithDate.updateTime instanceof Date ? 
    mockOrderWithDate.updateTime.toISOString() : 
    mockOrderWithDate.updateTime
};

console.log('\n✅ 修复后的订单数据:', fixedOrder);
console.log('✅ 修复后createTime类型:', typeof fixedOrder.createTime);
console.log('✅ 修复后createTime值:', fixedOrder.createTime);

const serializedFixedOrder = simulateSetDataSerialization(fixedOrder);
console.log('\n✅ 修复后setData序列化结果:', serializedFixedOrder);
console.log('✅ 序列化后createTime类型:', typeof serializedFixedOrder.createTime);
console.log('✅ 序列化后createTime值:', serializedFixedOrder.createTime);

// 4. 测试订单卡片的formatTime方法能否处理ISO字符串
function testOrderCardFormatTime(dateStr) {
  if (!dateStr) {
    return '未知';
  }

  // 处理Date对象和字符串两种情况
  const date = dateStr instanceof Date ? dateStr : new Date(dateStr);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date:', dateStr);
    return '未知';
  }

  const now = new Date();
  const diff = now - date;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`;
  } else {
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${month}-${day}`;
  }
}

console.log('\n🕐 测试时间格式化:');
console.log('原始Date对象格式化:', testOrderCardFormatTime(mockOrderWithDate.createTime));
console.log('序列化后对象格式化:', testOrderCardFormatTime(serializedOrder.createTime));
console.log('ISO字符串格式化:', testOrderCardFormatTime(fixedOrder.createTime));
console.log('序列化后ISO字符串格式化:', testOrderCardFormatTime(serializedFixedOrder.createTime));

// 5. 测试总结
console.log('\n📋 [测试总结]');
console.log('问题原因: setData会序列化Date对象，导致变成普通对象 {}');
console.log('解决方案: 在setData前将Date对象转换为ISO字符串');
console.log('修复效果: ✅ ISO字符串可以正确被new Date()解析');

// 6. 验证修复逻辑
const testCases = [
  { name: 'Date对象', value: new Date() },
  { name: 'ISO字符串', value: new Date().toISOString() },
  { name: '时间戳字符串', value: Date.now().toString() },
  { name: '普通对象', value: {} },
  { name: 'null', value: null },
  { name: 'undefined', value: undefined }
];

console.log('\n🧪 [验证修复逻辑]');
testCases.forEach(testCase => {
  const converted = testCase.value instanceof Date ? 
    testCase.value.toISOString() : 
    testCase.value;
  
  const formatted = testOrderCardFormatTime(converted);
  
  console.log(`${testCase.name}: ${testCase.value} → ${converted} → ${formatted}`);
});

console.log('\n💡 [建议] 发布一个新订单，观察:');
console.log('   1. 控制台中setData前后的createTime类型变化');
console.log('   2. 订单卡片是否正确显示时间（不是"未知"）');
console.log('   3. 时间格式是否为相对时间（如"刚刚"、"5分钟前"）');
