// 修复用户数据云函数
const cloud = require('wx-server-sdk');
const TimeZoneUtils = require('./common/timeZoneUtils');

cloud.init({
  env: 'cloud1-9gsj7t48183e5a9f'
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔧 开始修复用户数据');

  try {
    // 获取所有用户
    const usersResult = await db.collection('users').get();
    const users = usersResult.data;
    
    console.log('找到用户数量:', users.length);
    
    let updatedCount = 0;
    const now = TimeZoneUtils.createStorageTime();
    
    // 为每个用户添加 lastActiveTime 字段
    for (const user of users) {
      if (!user.lastActiveTime) {
        // 使用用户的 updateTime 作为初始活跃时间，如果没有则使用当前时间
        const initialTime = user.updateTime || user.createTime || now;
        
        await db.collection('users').doc(user._id).update({
          data: {
            lastActiveTime: initialTime
          }
        });
        
        updatedCount++;
        console.log(`✅ 已为用户 ${user.nickName || user._id} 添加 lastActiveTime 字段`);
      } else {
        console.log(`⏭️ 用户 ${user.nickName || user._id} 已有 lastActiveTime 字段，跳过`);
      }
    }
    
    console.log(`🎉 修复完成，共更新了 ${updatedCount} 个用户`);
    
    return {
      success: true,
      message: `成功为 ${updatedCount} 个用户添加 lastActiveTime 字段`,
      data: {
        totalUsers: users.length,
        updatedUsers: updatedCount
      }
    };
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
