请在微信开发者工具的控制台中运行以下代码来测试时间显示修复效果：

// 🧪 时间显示修复测试脚本 v4.0 - 彻底禁用所有时间字段修改
console.log('🧪 开始测试时间显示修复效果（v4.0）...');

// 1. 检查当前页面的订单数据
const currentPage = getCurrentPages()[getCurrentPages().length - 1];
console.log('📍 当前页面:', currentPage.route);

if (currentPage.route === 'pages/index/index') {
  const orders = currentPage.data.latestOrders || [];
  console.log('📊 首页订单数量:', orders.length);

  // 详细检查每个订单的时间数据
  orders.forEach((order, index) => {
    console.log(`📋 订单 ${index + 1}:`, {
      id: order._id?.substring(0, 8) + '...',
      createTime: order.createTime,
      createTimeType: typeof order.createTime,
      isValidTime: !!(order.createTime && order.createTime !== ''),
      formattedCreateTime: order.formattedCreateTime || '未设置'
    });
  });

  // 2. 测试页面的时间格式化方法
  console.log('\n🔧 测试时间格式化方法:');

  // 测试不同类型的时间输入
  const testCases = [
    { name: 'UTC字符串', value: new Date().toISOString() },
    { name: 'Date对象', value: new Date() },
    { name: '5分钟前', value: new Date(Date.now() - 5 * 60 * 1000).toISOString() },
    { name: '2小时前', value: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() },
    { name: '空值', value: null },
    { name: '空对象', value: {} }
  ];

  testCases.forEach(testCase => {
    try {
      const result = currentPage.formatTime(testCase.value);
      console.log(`  ${testCase.name}: ${testCase.value} → ${result}`);

      if (result === '未知' && testCase.value !== null && testCase.value !== {}) {
        console.warn(`  ⚠️ ${testCase.name} 返回"未知"，可能存在问题`);
      }
    } catch (error) {
      console.error(`  ❌ ${testCase.name} 测试失败:`, error);
    }
  });

  // 3. 检查组件状态
  console.log('\n🔍 检查订单卡片组件状态:');
  console.log('请观察页面上的订单卡片:');
  console.log('  ✅ 时间应该显示为相对时间（如"刚刚"、"5分钟前"）');
  console.log('  ❌ 不应该显示"未知"、"时间未知"或空白');
  console.log('  ⏳ 如果显示"..."或"时间加载中..."，说明数据正在加载');

} else {
  console.log('❌ 当前不在首页，请切换到首页进行测试');
}

// 4. 测试ensureTimeStringFormat方法
console.log('\n🔧 测试ensureTimeStringFormat方法:');
if (currentPage.route === 'pages/index/index') {
  const testValues = [
    { name: '有效ISO字符串', value: '2024-01-15T10:30:00.000Z' },
    { name: '有效Date对象', value: new Date() },
    { name: 'null值', value: null },
    { name: 'undefined值', value: undefined },
    { name: '空字符串', value: '' },
    { name: '空对象', value: {} },
    { name: '无效字符串', value: 'invalid-date' }
  ];

  testValues.forEach(test => {
    try {
      const result = currentPage.ensureTimeStringFormat(test.value);
      console.log(`  ${test.name}: ${JSON.stringify(test.value)} → ${result}`);

      // 检查是否意外将有效时间转换为null
      if (test.name.includes('有效') && result === null) {
        console.error(`  ❌ 错误：${test.name} 被意外转换为null`);
      }
    } catch (error) {
      console.error(`  ❌ ${test.name} 测试失败:`, error);
    }
  });
}

// 5. 刷新稳定性测试指导
console.log('\n🔄 刷新稳定性测试:');
console.log('1. 记录当前订单卡片的时间显示');
console.log('2. 刷新页面（下拉刷新或重新进入）');
console.log('3. 观察时间显示变化过程:');
console.log('   ✅ 正确：直接显示正确时间，保持稳定');
console.log('   ❌ 错误：先显示正确时间，然后变成"未知"或"时间未知"');
console.log('4. 等待500ms后观察是否有二次更新导致时间变化');

// 6. 监控所有可能的数据更新
console.log('\n🔍 监控数据更新方法:');
console.log('请在控制台中查找以下日志:');
console.log('  - "🔧 [页面显示] 跳过时间字段验证" - validateAndFixOrderTimeFields已禁用');
console.log('  - "🔧 [静默补充] 用户信息已更新" - enrichOrdersDataSilently仍在运行');
console.log('  - "🔧 [自动刷新] 跳过静默补充" - 自动刷新的静默补充已禁用');
console.log('  - 如果看到任何时间字段格式化的日志，说明还有遗漏的地方');

// 7. 实时监控时间变化
console.log('\n⏱️ 实时监控时间变化:');
if (currentPage.route === 'pages/index/index') {
  const orders = currentPage.data.latestOrders || [];
  if (orders.length > 0) {
    console.log('📊 当前订单时间快照:');
    orders.forEach((order, index) => {
      console.log(`  订单${index + 1}: ${order._id?.substring(0, 8)}... → ${order.createTime} (${typeof order.createTime})`);
    });

    // 设置监控定时器
    let checkCount = 0;
    const monitor = setInterval(() => {
      checkCount++;
      const currentOrders = getCurrentPages()[getCurrentPages().length - 1].data.latestOrders || [];

      console.log(`\n🔍 第${checkCount}次检查 (${checkCount * 2}秒后):`);
      currentOrders.forEach((order, index) => {
        const originalTime = orders[index]?.createTime;
        const currentTime = order.createTime;

        if (originalTime !== currentTime) {
          console.error(`❌ 订单${index + 1}时间发生变化: ${originalTime} → ${currentTime}`);
        } else {
          console.log(`✅ 订单${index + 1}时间保持稳定: ${currentTime}`);
        }
      });

      if (checkCount >= 5) { // 监控10秒
        clearInterval(monitor);
        console.log('\n🏁 监控结束');
      }
    }, 2000);
  }
}

console.log('\n✅ 测试脚本执行完成！');
console.log('🎯 关键检查点：时间显示应该保持完全稳定，不会有任何变化');
