// 公告横幅组件
Component({
  properties: {
    // 公告列表
    announcements: {
      type: Array,
      value: [],
      observer: function(newVal) {
        this.updateDisplayList(newVal);
      }
    },
    // 是否显示关闭按钮
    showCloseButton: {
      type: Boolean,
      value: false
    }
  },

  data: {
    showModal: false,
    selectedAnnouncement: {},
    currentIndex: 0,
    displayAnnouncements: [], // 用于显示的公告列表（包含复制的项）
    realLength: 0 // 真实公告数量
  },

  methods: {
    // 更新显示列表
    updateDisplayList(announcements) {
      if (!announcements || announcements.length === 0) {
        this.setData({
          displayAnnouncements: [],
          realLength: 0,
          currentIndex: 0
        });
        return;
      }

      const realLength = announcements.length;
      let displayList = [];

      if (realLength === 1) {
        // 只有一个公告时，不需要循环
        displayList = [...announcements];
      } else {
        // 多个公告时，手动构建循环：原始列表 + 第一个公告的副本
        displayList = [...announcements, { ...announcements[0], _id: announcements[0]._id + '_clone' }];
      }

      this.setData({
        displayAnnouncements: displayList,
        realLength: realLength,
        currentIndex: 0
      });
    },

    // swiper变化事件
    onSwiperChange(e) {
      const { current, source } = e.detail;
      const { realLength } = this.data;

      console.log('📢 [公告滚动] swiper变化:', { current, source, realLength });

      // 如果滚动到了克隆的第一个公告（最后一个位置）
      if (current === realLength && realLength > 1) {
        // 延迟无动画跳转到真正的第一个公告
        setTimeout(() => {
          this.setData({
            currentIndex: 0
          });
        }, 50);
      } else {
        this.setData({
          currentIndex: current
        });
      }
    },

    // swiper动画完成事件
    onSwiperAnimationFinish(e) {
      const { current } = e.detail;
      const { realLength } = this.data;

      console.log('📢 [公告滚动] 动画完成:', { current, realLength });

      // 如果在克隆的第一个公告位置，立即跳转到真正的第一个
      if (current === realLength && realLength > 1) {
        this.setData({
          currentIndex: 0
        });
      }
    },

    // 点击公告项
    onAnnouncementTap(e) {
      const announcement = e.currentTarget.dataset.announcement;
      const index = e.currentTarget.dataset.index;
      const { realLength } = this.data;

      if (!announcement) return;

      // 如果点击的是克隆项，使用原始数据
      let targetAnnouncement = announcement;
      if (index === realLength && realLength > 1) {
        targetAnnouncement = this.data.announcements[0];
      }

      console.log('点击公告:', targetAnnouncement.title);

      // 更新查看次数
      this.updateViewCount(targetAnnouncement._id);

      // 格式化时间显示 - 统一使用与云函数相同的格式化方法
      const formatTime = (timeStr) => {
        if (!timeStr) return '';
        try {
          const date = new Date(timeStr);
          if (isNaN(date.getTime())) {
            return timeStr;
          }

          // 🔧 修复时区一致性问题：
          // 存储的时间是UTC时间，需要转换为中国时区时间进行显示
          let displayDate = date;
          const chinaOffset = 8 * 60; // 中国时区偏移量（分钟）

          if (typeof timeStr === 'string' && timeStr.includes('T') && timeStr.includes('Z')) {
            // 这是UTC时间字符串，转换为中国时区时间进行显示
            displayDate = new Date(date.getTime() + chinaOffset * 60 * 1000);
          }

          // 使用统一的格式化方法，与云函数保持一致
          const year = displayDate.getFullYear();
          const month = String(displayDate.getMonth() + 1).padStart(2, '0');
          const day = String(displayDate.getDate()).padStart(2, '0');
          const hour = String(displayDate.getHours()).padStart(2, '0');
          const minute = String(displayDate.getMinutes()).padStart(2, '0');

          return `${year}-${month}-${day} ${hour}:${minute}`;
        } catch (error) {
          console.warn('🕐 [公告横幅] 时间格式化失败:', error, timeStr);
          return timeStr;
        }
      };

      // 准备弹窗数据
      const modalData = {
        ...targetAnnouncement,
        publishTimeFormatted: formatTime(targetAnnouncement.publishTime),
        expireTimeFormatted: formatTime(targetAnnouncement.expireTime)
      };

      // 显示弹窗
      this.setData({
        showModal: true,
        selectedAnnouncement: modalData
      });

      // 触发自定义事件
      this.triggerEvent('announcementTap', {
        announcement: targetAnnouncement
      });
    },

    // 关闭横幅
    onCloseBanner() {
      console.log('关闭公告横幅');

      // 触发自定义事件
      this.triggerEvent('closeBanner');
    },

    // 关闭弹窗
    onCloseModal() {
      console.log('关闭公告弹窗');

      this.setData({
        showModal: false,
        selectedAnnouncement: {}
      });
    },

    // 更新查看次数
    async updateViewCount(announcementId) {
      try {
        await wx.cloud.callFunction({
          name: 'announcementManager',
          data: {
            action: 'updateViewCount',
            announcementId: announcementId
          }
        });
      } catch (error) {
        console.error('更新公告查看次数失败:', error);
      }
    }
  }
});
