// 测试订单卡片时间更新功能的脚本
// 在微信开发者工具的控制台中运行此脚本

console.log('🧪 开始测试订单卡片时间更新功能...');

// 1. 模拟订单卡片的时间格式化逻辑
function mockFormatTime(dateStr) {
  console.log('🕐 [测试] formatTime输入:', {
    value: dateStr,
    type: typeof dateStr,
    isDate: dateStr instanceof Date
  });

  // 检查空值或无效值
  if (!dateStr || dateStr === '' || dateStr === null || dateStr === undefined) {
    console.log('🕐 [测试] 返回"未知" - 空值或无效值');
    return '未知';
  }

  // 检查是否为空对象（setData序列化Date对象后的结果）
  if (typeof dateStr === 'object' && dateStr !== null && !(dateStr instanceof Date)) {
    if (Object.keys(dateStr).length === 0) {
      console.warn('🕐 [测试] 检测到空对象，可能是setData序列化Date对象的结果:', dateStr);
      return '未知';
    }
  }

  let date;

  // 处理不同类型的时间输入
  if (dateStr instanceof Date) {
    date = dateStr;
  } else if (typeof dateStr === 'string') {
    // 尝试解析字符串格式的时间
    date = new Date(dateStr);
  } else if (typeof dateStr === 'number') {
    // 处理时间戳
    date = new Date(dateStr);
  } else {
    console.warn('🕐 [测试] 不支持的时间格式:', typeof dateStr, dateStr);
    return '未知';
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('🕐 [测试] Invalid date:', dateStr, '转换后的date:', date);
    return '未知';
  }

  const now = new Date();
  const diff = now - date;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`;
  } else {
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${month}-${day}`;
  }
}

// 2. 测试时间更新逻辑
function testTimeUpdate() {
  console.log('\n⏰ [测试] 时间更新逻辑测试');

  // 创建不同时间点的测试数据
  const now = new Date();
  const testTimes = [
    new Date(now.getTime() - 30000),    // 30秒前
    new Date(now.getTime() - 120000),   // 2分钟前
    new Date(now.getTime() - 1800000),  // 30分钟前
    new Date(now.getTime() - 3900000),  // 65分钟前
    new Date(now.getTime() - 7200000),  // 2小时前
    new Date(now.getTime() - 86400000), // 1天前
  ];

  console.log('初始时间显示:');
  testTimes.forEach((time, index) => {
    const formatted = mockFormatTime(time);
    console.log(`测试时间${index + 1}: ${formatted}`);
  });

  // 模拟30秒后的时间更新
  console.log('\n30秒后的时间显示:');
  const futureNow = new Date(now.getTime() + 30000);
  testTimes.forEach((time, index) => {
    // 模拟30秒后重新计算
    const diff = futureNow - time;
    let formatted;
    
    if (diff < 60000) {
      formatted = '刚刚';
    } else if (diff < 3600000) {
      formatted = `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      formatted = `${Math.floor(diff / 3600000)}小时前`;
    } else {
      const month = String(time.getMonth() + 1).padStart(2, '0');
      const day = String(time.getDate()).padStart(2, '0');
      formatted = `${month}-${day}`;
    }
    
    console.log(`测试时间${index + 1}: ${formatted}`);
  });
}

// 3. 测试定时器管理
function testTimerManagement() {
  console.log('\n⏰ [测试] 定时器管理测试');

  let timeUpdateTimer = null;
  let updateCount = 0;

  // 模拟启动时间更新定时器
  function startTimeUpdateTimer() {
    // 清除现有定时器
    if (timeUpdateTimer) {
      clearInterval(timeUpdateTimer);
      console.log('🕐 [测试] 清除现有定时器');
    }

    // 为了测试，使用较短的间隔（1秒）
    timeUpdateTimer = setInterval(() => {
      updateCount++;
      console.log(`🕐 [测试] 第${updateCount}次时间更新`);
      
      // 测试3次后停止
      if (updateCount >= 3) {
        stopTimeUpdateTimer();
      }
    }, 1000);

    console.log('🕐 [测试] 启动时间更新定时器');
  }

  // 模拟停止时间更新定时器
  function stopTimeUpdateTimer() {
    if (timeUpdateTimer) {
      clearInterval(timeUpdateTimer);
      timeUpdateTimer = null;
      console.log('🕐 [测试] 停止时间更新定时器');
    }
  }

  // 执行测试
  startTimeUpdateTimer();

  // 5秒后检查结果
  setTimeout(() => {
    console.log(`测试完成，共执行了${updateCount}次更新`);
    console.log('定时器管理测试:', updateCount === 3 ? '✅ 通过' : '❌ 失败');
  }, 4000);
}

// 4. 测试页面生命周期管理
function testPageLifecycle() {
  console.log('\n🔄 [测试] 页面生命周期管理测试');

  let timeUpdateTimer = null;
  let isPageVisible = true;

  function startTimeUpdateTimer() {
    if (timeUpdateTimer) {
      clearInterval(timeUpdateTimer);
    }

    timeUpdateTimer = setInterval(() => {
      if (isPageVisible) {
        console.log('🕐 [测试] 页面可见，更新时间显示');
      } else {
        console.log('⏸️ [测试] 页面不可见，但定时器仍在运行（这是错误的）');
      }
    }, 1000);

    console.log('✅ 时间更新定时器已启动');
  }

  function stopTimeUpdateTimer() {
    if (timeUpdateTimer) {
      clearInterval(timeUpdateTimer);
      timeUpdateTimer = null;
      console.log('🛑 时间更新定时器已停止');
    }
  }

  function onPageShow() {
    console.log('📱 页面显示');
    isPageVisible = true;
    startTimeUpdateTimer();
  }

  function onPageHide() {
    console.log('📱 页面隐藏');
    isPageVisible = false;
    stopTimeUpdateTimer();
  }

  // 模拟页面生命周期
  onPageShow();
  
  setTimeout(() => {
    onPageHide();
  }, 2500);

  setTimeout(() => {
    onPageShow();
  }, 4000);

  setTimeout(() => {
    onPageHide();
    console.log('页面生命周期测试完成');
  }, 6000);
}

// 5. 测试实际订单数据的时间更新
function testRealOrderData() {
  console.log('\n📋 [测试] 实际订单数据时间更新测试');

  // 模拟真实的订单数据
  const mockOrder = {
    _id: 'test_order_123',
    createTime: new Date(Date.now() - 300000), // 5分钟前
    title: '测试订单',
    status: 'pending'
  };

  console.log('初始订单时间:', mockFormatTime(mockOrder.createTime));

  // 模拟30秒后的更新
  setTimeout(() => {
    console.log('30秒后订单时间:', mockFormatTime(mockOrder.createTime));
  }, 1000);

  // 模拟1分钟后的更新
  setTimeout(() => {
    console.log('1分钟后订单时间:', mockFormatTime(mockOrder.createTime));
  }, 2000);
}

// 执行所有测试
console.log('🚀 开始执行订单卡片时间更新测试...\n');

testTimeUpdate();

setTimeout(() => {
  testTimerManagement();
}, 1000);

setTimeout(() => {
  testPageLifecycle();
}, 2000);

setTimeout(() => {
  testRealOrderData();
}, 8000);

// 测试总结
setTimeout(() => {
  console.log('\n📋 [测试总结]');
  console.log('✅ 订单卡片时间更新功能测试完成');
  console.log('');
  console.log('🔧 实现的功能:');
  console.log('   1. 每30秒自动更新订单卡片的时间显示');
  console.log('   2. 页面显示时启动时间更新定时器');
  console.log('   3. 页面隐藏时停止时间更新定时器');
  console.log('   4. 组件销毁时自动清理定时器');
  console.log('   5. 支持多种时间格式的处理');
  console.log('');
  console.log('💡 [效果]:');
  console.log('   - "5分钟前" 会自动更新为 "6分钟前"');
  console.log('   - "刚刚" 会自动更新为 "1分钟前"');
  console.log('   - 时间显示始终保持准确和实时');
  console.log('');
  console.log('🔍 [调试信息]:');
  console.log('   - 查看控制台中的"🕐 [订单卡片]"日志');
  console.log('   - 监控定时器的启动和停止状态');
}, 12000);
