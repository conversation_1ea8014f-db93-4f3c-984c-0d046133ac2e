// 简化版创建公告脚本 - 在数据库集合创建后使用
// 请在微信开发者工具的控制台中运行此代码

console.log('🚀 开始创建测试公告（简化版）...');

// 直接使用数据库API创建公告
async function createAnnouncementDirectly() {
  try {
    console.log('📝 直接向数据库添加公告...');
    
    const db = wx.cloud.database();
    const announcementsCollection = db.collection('announcements');
    
    // 测试公告数据
    const testAnnouncement = {
      title: '🎉 欢迎使用三角洲接单平台！',
      content: '亲爱的用户，欢迎来到三角洲接单平台！我们致力于为您提供最优质的游戏代练服务。\n\n✨ 平台特色功能：\n• 智能任务匹配系统\n• 安全保障机制\n• 便捷的钱包系统\n• 24小时客服支持\n\n如有任何问题，请随时联系我们的客服团队。祝您使用愉快！',
      type: 'notice',
      priority: 1,
      status: 'active',
      publishTime: new Date(),
      effectiveTime: new Date(),
      expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
      isTop: true,
      viewCount: 0,
      createdBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await announcementsCollection.add({
      data: testAnnouncement
    });
    
    if (result._id) {
      console.log('✅ 公告创建成功！');
      console.log('📄 公告ID:', result._id);
      console.log('📝 公告标题:', testAnnouncement.title);
      
      // 验证公告是否可以查询到
      const queryResult = await announcementsCollection.where({
        _id: result._id
      }).get();
      
      if (queryResult.data && queryResult.data.length > 0) {
        console.log('✅ 公告查询验证成功！');
        console.log('📊 公告数据:', queryResult.data[0]);
      } else {
        console.log('⚠️ 公告查询验证失败');
      }
      
    } else {
      console.error('❌ 公告创建失败:', result);
    }
    
  } catch (error) {
    console.error('❌ 创建公告异常:', error);
  }
}

// 创建多个测试公告
async function createMultipleAnnouncements() {
  try {
    console.log('📝 创建多个测试公告...');
    
    const db = wx.cloud.database();
    const announcementsCollection = db.collection('announcements');
    
    const announcements = [
      {
        title: '🎉 欢迎使用三角洲接单平台！',
        content: '亲爱的用户，欢迎来到三角洲接单平台！我们致力于为您提供最优质的游戏代练服务。',
        type: 'notice',
        priority: 1,
        status: 'active',
        isTop: true
      },
      {
        title: '🔧 系统维护通知',
        content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用，请提前做好准备。',
        type: 'system',
        priority: 1,
        status: 'active',
        isTop: true
      },
      {
        title: '🚨 安全提醒',
        content: '近期发现有不法分子冒充平台客服进行诈骗，请大家提高警惕！请通过官方渠道联系我们。',
        type: 'urgent',
        priority: 1,
        status: 'active',
        isTop: false
      }
    ];
    
    for (let i = 0; i < announcements.length; i++) {
      const announcement = announcements[i];
      
      const data = {
        ...announcement,
        publishTime: new Date(),
        effectiveTime: new Date(),
        expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        viewCount: 0,
        createdBy: 'admin',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      try {
        const result = await announcementsCollection.add({ data });
        
        if (result._id) {
          console.log(`✅ 公告 ${i + 1} 创建成功: ${announcement.title}`);
          console.log(`   ID: ${result._id}`);
        } else {
          console.error(`❌ 公告 ${i + 1} 创建失败:`, result);
        }
        
        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        console.error(`❌ 公告 ${i + 1} 创建异常:`, error);
      }
    }
    
    // 查询所有公告验证
    console.log('🔍 查询所有公告验证...');
    const allResult = await announcementsCollection.where({
      status: 'active'
    }).orderBy('isTop', 'desc').orderBy('priority', 'asc').get();
    
    if (allResult.data && allResult.data.length > 0) {
      console.log('✅ 公告列表查询成功，共', allResult.data.length, '条公告');
      
      allResult.data.forEach((item, index) => {
        console.log(`📄 公告 ${index + 1}:`, {
          id: item._id,
          title: item.title,
          type: item.type,
          status: item.status,
          isTop: item.isTop
        });
      });
      
      console.log('🎉 所有测试公告创建完成！');
      console.log('💡 现在您可以在小程序首页看到公告横幅了');
      console.log('🔄 请刷新小程序首页查看效果');
      
    } else {
      console.log('⚠️ 没有查询到公告数据');
    }
    
  } catch (error) {
    console.error('❌ 创建多个公告异常:', error);
  }
}

// 测试云函数是否正常工作
async function testCloudFunction() {
  try {
    console.log('🧪 测试云函数...');
    
    const result = await wx.cloud.callFunction({
      name: 'getAnnouncementList',
      data: {
        page: 1,
        pageSize: 10,
        status: 'active',
        isAdmin: false
      }
    });
    
    console.log('云函数测试结果:', result);
    
    if (result.result && result.result.success) {
      console.log('✅ 云函数工作正常');
      const announcements = result.result.data.list;
      console.log('📊 通过云函数获取到', announcements.length, '条公告');
    } else {
      console.error('❌ 云函数测试失败:', result.result?.error || result.errMsg);
    }
    
  } catch (error) {
    console.error('❌ 云函数测试异常:', error);
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.createAnnouncementDirectly = createAnnouncementDirectly;
  window.createMultipleAnnouncements = createMultipleAnnouncements;
  window.testCloudFunction = testCloudFunction;
}

console.log('📋 简化版公告创建脚本加载完成！');
console.log('💡 使用方法：');
console.log('  - 创建单个公告: createAnnouncementDirectly()');
console.log('  - 创建多个公告: createMultipleAnnouncements()');
console.log('  - 测试云函数: testCloudFunction()');

console.log('⚠️ 请确保已在云开发控制台创建了 "announcements" 集合！');
console.log('🔄 自动开始创建公告...');

// 自动执行
createMultipleAnnouncements();
