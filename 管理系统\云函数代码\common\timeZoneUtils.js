/**
 * 统一时区处理工具
 * 用于确保所有云函数使用一致的中国时区(GMT+8)处理
 */

const TimeZoneUtils = {
  // 中国时区偏移量（分钟）
  CHINA_OFFSET: 8 * 60,
  
  /**
   * 获取当前中国时间
   * @returns {Date} 中国时区的当前时间
   */
  getChinaTime() {
    const now = new Date();
    return new Date(now.getTime() + this.CHINA_OFFSET * 60 * 1000);
  },
  
  /**
   * 获取中国时区的今日开始时间（00:00:00）
   * @returns {Date} 中国时区今日开始时间
   */
  getChinaTodayStart() {
    const chinaTime = this.getChinaTime();
    return new Date(chinaTime.getFullYear(), chinaTime.getMonth(), chinaTime.getDate(), 0, 0, 0, 0);
  },
  
  /**
   * 获取用于数据库查询的UTC时间
   * @param {Date} chinaTime 中国时区时间
   * @returns {Date} 对应的UTC时间
   */
  chinaTimeToUTC(chinaTime) {
    return new Date(chinaTime.getTime() - this.CHINA_OFFSET * 60 * 1000);
  },
  
  /**
   * 将UTC时间转换为中国时区时间
   * @param {Date} utcTime UTC时间
   * @returns {Date} 中国时区时间
   */
  utcToChinaTime(utcTime) {
    return new Date(utcTime.getTime() + this.CHINA_OFFSET * 60 * 1000);
  },
  
  /**
   * 获取今日开始时间的UTC版本（用于数据库查询）
   * @returns {Date} 今日开始时间的UTC版本
   */
  getTodayStartUTC() {
    const todayStart = this.getChinaTodayStart();
    return this.chinaTimeToUTC(todayStart);
  },
  
  /**
   * 获取指定时间前的UTC时间（用于数据库查询）
   * @param {number} minutes 分钟数
   * @returns {Date} 指定时间前的UTC时间
   */
  getTimeAgoUTC(minutes) {
    const chinaTime = this.getChinaTime();
    const timeAgo = new Date(chinaTime.getTime() - minutes * 60 * 1000);
    return this.chinaTimeToUTC(timeAgo);
  },
  
  /**
   * 创建用于存储的时间（统一使用中国时区时间转换为UTC存储）
   * @param {Date} [time] 可选的时间，默认为当前中国时间
   * @returns {Date} 用于存储的UTC时间
   */
  createStorageTime(time) {
    const chinaTime = time || this.getChinaTime();
    return this.chinaTimeToUTC(chinaTime);
  },
  
  /**
   * 获取中国时区的日期字符串（YYYY-MM-DD格式）
   * @param {Date} [time] 可选的时间，默认为当前中国时间
   * @returns {string} 日期字符串
   */
  getChinaDateString(time) {
    const chinaTime = time || this.getChinaTime();
    const year = chinaTime.getFullYear();
    const month = String(chinaTime.getMonth() + 1).padStart(2, '0');
    const day = String(chinaTime.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },
  
  /**
   * 获取中国时区的日期时间字符串（YYYY-MM-DD HH:mm:ss格式）
   * @param {Date} [time] 可选的时间，默认为当前中国时间
   * @returns {string} 日期时间字符串
   */
  getChinaDateTimeString(time) {
    const chinaTime = time || this.getChinaTime();
    const year = chinaTime.getFullYear();
    const month = String(chinaTime.getMonth() + 1).padStart(2, '0');
    const day = String(chinaTime.getDate()).padStart(2, '0');
    const hour = String(chinaTime.getHours()).padStart(2, '0');
    const minute = String(chinaTime.getMinutes()).padStart(2, '0');
    const second = String(chinaTime.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  },
  
  /**
   * 调试信息：打印时区相关信息
   * @param {string} context 上下文信息
   */
  logTimeZoneInfo(context = '') {
    const now = new Date();
    const chinaTime = this.getChinaTime();
    const todayStart = this.getChinaTodayStart();
    const todayStartUTC = this.getTodayStartUTC();
    
    console.log(`🕐 [${context}] 时区信息:`);
    console.log(`   服务器时间: ${now.toISOString()}`);
    console.log(`   中国时间: ${chinaTime.toISOString()}`);
    console.log(`   今日开始(中国): ${todayStart.toISOString()}`);
    console.log(`   今日开始(UTC): ${todayStartUTC.toISOString()}`);
    console.log(`   时区偏移: ${now.getTimezoneOffset()} 分钟`);
  }
};

module.exports = TimeZoneUtils;
