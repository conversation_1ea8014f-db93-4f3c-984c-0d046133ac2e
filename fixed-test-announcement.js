// 修复版测试公告创建脚本
// 请在微信开发者工具的控制台中运行此代码

console.log('🚀 开始添加测试公告（修复版）...');

// 测试公告数据
const testAnnouncements = [
  {
    title: '🎉 欢迎使用三角洲接单平台！',
    content: '亲爱的用户，欢迎来到三角洲接单平台！我们致力于为您提供最优质的游戏代练服务。\n\n✨ 平台特色功能：\n• 智能任务匹配系统\n• 安全保障机制\n• 便捷的钱包系统\n• 24小时客服支持\n\n如有任何问题，请随时联系我们的客服团队。祝您使用愉快！',
    type: 'notice',
    priority: 1,
    status: 'active',
    isTop: true
  },
  {
    title: '🔧 系统维护通知',
    content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用，请提前做好准备。维护完成后将为大家带来更好的使用体验！',
    type: 'system',
    priority: 1,
    status: 'active',
    isTop: true
  },
  {
    title: '🚨 安全提醒',
    content: '近期发现有不法分子冒充平台客服进行诈骗，请大家提高警惕！\n\n⚠️ 重要提醒：\n• 平台客服不会主动要求您提供密码\n• 不会要求您进行转账操作\n• 如有疑问请通过官方渠道联系我们\n\n保护好您的账户安全！',
    type: 'urgent',
    priority: 1,
    status: 'active',
    isTop: false
  }
];

// 添加测试公告的函数
async function addTestAnnouncementsFixed() {
  try {
    console.log('📊 初始化公告数据库...');
    
    // 首先初始化数据库
    const initResult = await wx.cloud.callFunction({
      name: 'initAnnouncementDatabase'
    });
    
    console.log('初始化结果:', initResult);
    
    if (initResult.result && initResult.result.success) {
      console.log('✅ 数据库初始化成功');
    } else {
      console.log('ℹ️ 数据库可能已经初始化过了，继续创建公告...');
    }
    
    // 等待一下确保数据库初始化完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('📝 开始创建测试公告...');
    
    // 创建测试公告
    for (let i = 0; i < testAnnouncements.length; i++) {
      const announcement = testAnnouncements[i];
      
      try {
        console.log(`正在创建公告 ${i + 1}: ${announcement.title}`);
        
        const result = await wx.cloud.callFunction({
          name: 'announcementManager',
          data: {
            action: 'create',
            title: announcement.title,
            content: announcement.content,
            type: announcement.type,
            priority: announcement.priority,
            status: announcement.status,
            publishTime: new Date(),
            effectiveTime: new Date(),
            expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
            isTop: announcement.isTop
          }
        });
        
        console.log(`公告 ${i + 1} 创建结果:`, result);
        
        if (result.result && result.result.success) {
          console.log(`✅ 公告 ${i + 1} 创建成功: ${announcement.title}`);
          console.log(`   ID: ${result.result.data._id}`);
        } else {
          console.error(`❌ 公告 ${i + 1} 创建失败:`, result.result?.error || result.errMsg);
        }
        
        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1500));
        
      } catch (error) {
        console.error(`❌ 公告 ${i + 1} 创建异常:`, error);
      }
    }
    
    console.log('🔍 验证公告列表...');
    
    // 等待一下确保数据写入完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 验证公告是否创建成功
    const listResult = await wx.cloud.callFunction({
      name: 'getAnnouncementList',
      data: {
        page: 1,
        pageSize: 10,
        status: 'active',
        isAdmin: false
      }
    });
    
    console.log('公告列表查询结果:', listResult);
    
    if (listResult.result && listResult.result.success) {
      const announcements = listResult.result.data.list;
      console.log('✅ 公告列表获取成功，共', announcements.length, '条公告');
      
      announcements.forEach((item, index) => {
        console.log(`📄 公告 ${index + 1}:`, {
          id: item._id,
          title: item.title,
          type: item.type,
          status: item.status,
          isTop: item.isTop,
          publishTime: item.publishTime
        });
      });
      
      if (announcements.length > 0) {
        console.log('🎉 测试公告创建成功！');
        console.log('💡 现在您可以在小程序首页看到公告横幅了');
        console.log('🔄 请刷新小程序首页查看效果');
      } else {
        console.log('⚠️ 没有找到公告，可能创建失败');
      }
    } else {
      console.error('❌ 公告列表获取失败:', listResult.result?.error || listResult.errMsg);
    }
    
  } catch (error) {
    console.error('❌ 添加测试公告时发生异常:', error);
  }
}

// 简化版创建单个公告的函数
async function createSingleAnnouncement() {
  try {
    console.log('📝 创建单个测试公告...');
    
    const result = await wx.cloud.callFunction({
      name: 'announcementManager',
      data: {
        action: 'create',
        title: '🎉 欢迎使用三角洲接单平台！',
        content: '亲爱的用户，欢迎来到三角洲接单平台！我们致力于为您提供最优质的游戏代练服务。',
        type: 'notice',
        priority: 1,
        status: 'active',
        publishTime: new Date(),
        effectiveTime: new Date(),
        expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        isTop: true
      }
    });
    
    console.log('创建结果:', result);
    
    if (result.result && result.result.success) {
      console.log('✅ 单个公告创建成功！');
      console.log('ID:', result.result.data._id);
    } else {
      console.error('❌ 单个公告创建失败:', result.result?.error || result.errMsg);
    }
    
  } catch (error) {
    console.error('❌ 创建单个公告异常:', error);
  }
}

// 检查数据库集合是否存在
async function checkDatabase() {
  try {
    console.log('🔍 检查数据库状态...');
    
    const result = await wx.cloud.callFunction({
      name: 'getAnnouncementList',
      data: {
        page: 1,
        pageSize: 1,
        isAdmin: true
      }
    });
    
    console.log('数据库检查结果:', result);
    
    if (result.result && result.result.success) {
      console.log('✅ 数据库集合存在');
      return true;
    } else {
      console.log('❌ 数据库集合不存在或有问题');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 数据库检查异常:', error);
    return false;
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.addTestAnnouncementsFixed = addTestAnnouncementsFixed;
  window.createSingleAnnouncement = createSingleAnnouncement;
  window.checkDatabase = checkDatabase;
}

console.log('📋 修复版测试公告创建脚本加载完成！');
console.log('💡 使用方法：');
console.log('  - 检查数据库: checkDatabase()');
console.log('  - 创建单个公告: createSingleAnnouncement()');
console.log('  - 创建多个公告: addTestAnnouncementsFixed()');

// 先检查数据库状态
console.log('🔄 自动检查数据库状态...');
checkDatabase().then(exists => {
  if (exists) {
    console.log('✅ 数据库正常，可以创建公告');
    console.log('🚀 开始自动创建测试公告...');
    addTestAnnouncementsFixed();
  } else {
    console.log('⚠️ 数据库需要初始化，请先运行初始化');
    console.log('🔄 尝试初始化数据库...');
    wx.cloud.callFunction({
      name: 'initAnnouncementDatabase'
    }).then(result => {
      console.log('初始化完成，等待3秒后创建公告...');
      setTimeout(() => {
        addTestAnnouncementsFixed();
      }, 3000);
    });
  }
});
