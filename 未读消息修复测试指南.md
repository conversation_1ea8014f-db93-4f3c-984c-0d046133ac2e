# 未读消息显示问题修复测试指南

## 问题描述
用户A退出小程序后，用户B给用户A发送消息，用户A重新进入小程序时未读数不显示的问题。

## 修复内容概述

### 1. 离线消息检查逻辑修复
- 修复时间选择逻辑，使用正确的离线时间
- 添加消息去重机制，防止重复计数
- 优化时间更新策略

### 2. 未读消息状态管理优化
- 强制保存状态到本地存储
- 增强调试日志
- 改进初始化逻辑

### 3. TabBar显示修复
- 延迟初始化机制
- 强制更新机制
- 启动时同步

### 4. 时序问题修复
- 调整执行顺序
- 防止竞态条件

## 测试步骤

### 测试环境准备
1. 准备两个测试账号：用户A和用户B
2. 确保两个用户之间有聊天记录
3. 清理小程序缓存（可选）

### 测试场景1：基本离线消息测试
1. **用户A操作**：
   - 登录小程序
   - 进入聊天列表页面
   - 退出小程序（按Home键或切换应用）

2. **用户B操作**：
   - 登录小程序
   - 给用户A发送1-2条消息
   - 确认消息发送成功

3. **用户A验证**：
   - 重新进入小程序
   - 检查TabBar是否显示正确的未读数
   - 检查聊天列表是否显示未读消息
   - 检查控制台日志是否有离线消息检查记录

### 测试场景2：多聊天室离线消息测试
1. **准备**：用户A与多个用户有聊天记录
2. **用户A退出小程序**
3. **多个用户给用户A发送消息**
4. **用户A重新进入，验证**：
   - TabBar显示总未读数
   - 聊天列表显示各聊天室的未读数
   - 未读数计算正确

### 测试场景3：快速切换测试
1. **用户A**：进入小程序 → 退出 → 快速重新进入
2. **用户B**：在用户A快速切换期间发送消息
3. **验证**：未读数是否正确显示

## 关键检查点

### 1. 控制台日志检查
查看以下关键日志：
```
📱 [离线消息检查] 方法被调用
📱 [离线消息检查] 开始检查离线期间的新消息
📱 [离线消息检查] 发现离线消息: X 条
📱 [离线消息检查] 总共处理了 X 条消息
📱 [TabBar] 收到未读消息更新事件
📱 [聊天列表] 收到离线消息检测事件
```

### 2. 数据状态检查
在开发者工具中检查：
- `app.globalData.unreadMessages.total` 是否正确
- `app.globalData.unreadMessages.chatRooms` 是否包含正确的聊天室数据
- 本地存储中的 `unreadMessages` 是否正确保存

### 3. UI显示检查
- TabBar聊天图标是否显示红色数字徽章
- 聊天列表中相应聊天室是否显示未读数
- 未读数是否与实际未读消息数量一致

## 常见问题排查

### 问题1：TabBar不显示未读数
**排查步骤**：
1. 检查 `custom-tab-bar/index.js` 是否正确初始化
2. 检查 `app.globalData.unreadMessages.total` 的值
3. 检查是否有 `unreadMessageUpdate` 事件触发

### 问题2：未读数不准确
**排查步骤**：
1. 检查是否有消息重复计数
2. 检查离线消息检查的时间范围是否正确
3. 检查消息去重机制是否正常工作

### 问题3：离线消息检查不执行
**排查步骤**：
1. 检查 `app.globalData.lastHideTime` 是否正确设置
2. 检查用户信息是否正确加载
3. 检查云函数调用是否成功

## 预期结果
修复后，用户A退出小程序再重新进入时：
1. TabBar应该显示正确的未读消息数
2. 聊天列表应该显示各聊天室的未读状态
3. 控制台应该有完整的离线消息检查日志
4. 不应该出现消息重复计数的情况

## 注意事项
1. 测试时注意观察控制台日志，确保各个环节正常执行
2. 如果问题仍然存在，请提供详细的控制台日志用于进一步分析
3. 建议在不同的网络环境下进行测试
4. 测试时可以适当增加消息发送的间隔，避免网络延迟影响
