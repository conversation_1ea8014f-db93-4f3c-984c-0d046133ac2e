// 测试订单卡片时间显示修复效果的脚本
// 在微信开发者工具的控制台中运行此脚本

console.log('🧪 开始测试订单卡片时间显示修复效果...');

// 1. 模拟TimeZoneUtils.createStorageTime()的返回值
function mockCreateStorageTime() {
  const now = new Date();
  const chinaOffset = 8 * 60; // 中国时区偏移量（分钟）
  const chinaTime = new Date(now.getTime() + chinaOffset * 60 * 1000);
  // 转换为UTC存储时间
  const utcTime = new Date(chinaTime.getTime() - chinaOffset * 60 * 1000);
  return utcTime;
}

// 2. 模拟修复后的订单卡片formatTime方法
function testOrderCardFormatTime(dateStr) {
  console.log('🕐 [订单卡片测试] formatTime输入:', {
    value: dateStr,
    type: typeof dateStr,
    isDate: dateStr instanceof Date,
    stringValue: String(dateStr)
  });

  // 检查空值或无效值
  if (!dateStr || dateStr === '' || dateStr === null || dateStr === undefined) {
    console.log('🕐 [订单卡片测试] 返回"未知" - 空值或无效值');
    return '未知';
  }

  // 检查是否为空对象（setData序列化Date对象后的结果）
  if (typeof dateStr === 'object' && dateStr !== null && !(dateStr instanceof Date)) {
    if (Object.keys(dateStr).length === 0) {
      console.warn('🕐 [订单卡片测试] 检测到空对象，可能是setData序列化Date对象的结果:', dateStr);
      return '未知';
    }
  }

  let date;

  // 处理不同类型的时间输入
  if (dateStr instanceof Date) {
    date = dateStr;
  } else if (typeof dateStr === 'string') {
    // 尝试解析字符串格式的时间
    date = new Date(dateStr);
  } else if (typeof dateStr === 'number') {
    // 处理时间戳
    date = new Date(dateStr);
  } else {
    console.warn('🕐 [订单卡片测试] 不支持的时间格式:', typeof dateStr, dateStr);
    return '未知';
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('🕐 [订单卡片测试] Invalid date:', dateStr, '转换后的date:', date);
    return '未知';
  }

  // 🔧 修复时区一致性问题：
  // 存储的时间是UTC时间，需要转换为中国时区时间进行显示
  // 获取当前中国时间用于计算时间差
  const now = new Date();
  const chinaOffset = 8 * 60; // 中国时区偏移量（分钟）
  const chinaTime = new Date(now.getTime() + chinaOffset * 60 * 1000);
  
  // 如果传入的是ISO字符串（UTC时间），需要转换为中国时区时间
  let displayDate = date;
  if (typeof dateStr === 'string' && dateStr.includes('T') && dateStr.includes('Z')) {
    // 这是UTC时间字符串，转换为中国时区时间进行显示
    displayDate = new Date(date.getTime() + chinaOffset * 60 * 1000);
    console.log('🕐 [订单卡片测试] UTC时间转换为中国时区:', {
      原始UTC: dateStr,
      UTC时间: date.toISOString(),
      中国时间: displayDate.toISOString(),
      当前中国时间: chinaTime.toISOString()
    });
  }

  const diff = chinaTime - displayDate;
  
  console.log('🕐 [订单卡片测试] 时间差计算:', {
    当前中国时间: chinaTime.toISOString(),
    订单时间: displayDate.toISOString(),
    时间差毫秒: diff,
    时间差分钟: Math.floor(diff / 60000)
  });

  // 处理负时间差（未来时间）
  if (diff < 0) {
    console.warn('🕐 [订单卡片测试] 检测到未来时间，可能存在时区问题:', {
      diff: diff,
      订单时间: displayDate.toISOString(),
      当前时间: chinaTime.toISOString()
    });
    return '刚刚'; // 对于未来时间，显示为"刚刚"
  }

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`;
  } else {
    // 使用中国时区的日期进行格式化
    const month = String(displayDate.getMonth() + 1).padStart(2, '0');
    const day = String(displayDate.getDate()).padStart(2, '0');
    return `${month}-${day}`;
  }
}

// 3. 测试各种可能导致"未知"的情况
console.log('\n🧪 [测试] 各种可能导致"未知"的情况:');

const problemCases = [
  {
    name: 'TimeZoneUtils.createStorageTime()返回的Date对象',
    value: mockCreateStorageTime(),
    expected: '应该显示相对时间，不应该是"未知"'
  },
  {
    name: 'UTC时间字符串',
    value: mockCreateStorageTime().toISOString(),
    expected: '应该显示相对时间，不应该是"未知"'
  },
  {
    name: 'setData序列化后的空对象',
    value: {},
    expected: '应该显示"未知"'
  },
  {
    name: 'null值',
    value: null,
    expected: '应该显示"未知"'
  },
  {
    name: 'undefined值',
    value: undefined,
    expected: '应该显示"未知"'
  },
  {
    name: '空字符串',
    value: '',
    expected: '应该显示"未知"'
  },
  {
    name: '无效字符串',
    value: 'invalid-date',
    expected: '应该显示"未知"'
  }
];

problemCases.forEach(testCase => {
  console.log(`\n📋 测试: ${testCase.name}`);
  console.log(`输入值: ${JSON.stringify(testCase.value)}`);
  console.log(`期望结果: ${testCase.expected}`);
  
  const result = testOrderCardFormatTime(testCase.value);
  console.log(`实际结果: ${result}`);
  
  // 检查结果是否符合预期
  if (testCase.expected.includes('未知') && result === '未知') {
    console.log('✅ 测试通过');
  } else if (testCase.expected.includes('相对时间') && result !== '未知') {
    console.log('✅ 测试通过');
  } else {
    console.log('❌ 测试失败');
  }
});

// 4. 测试时区转换的正确性
console.log('\n🌍 [测试] 时区转换正确性:');

const utcTime = mockCreateStorageTime();
const utcString = utcTime.toISOString();
const chinaOffset = 8 * 60;
const chinaTime = new Date(utcTime.getTime() + chinaOffset * 60 * 1000);

console.log('UTC存储时间:', utcString);
console.log('转换后的中国时间:', chinaTime.toISOString());
console.log('时差（小时）:', (chinaTime.getTime() - utcTime.getTime()) / (1000 * 60 * 60));

// 5. 模拟页面切换场景
console.log('\n🔄 [测试] 模拟页面切换场景:');

// 模拟订单数据
const mockOrder = {
  _id: 'test-order-id',
  createTime: mockCreateStorageTime(), // Date对象
  title: '测试订单'
};

console.log('原始订单数据:', {
  createTime: mockOrder.createTime,
  type: typeof mockOrder.createTime,
  isDate: mockOrder.createTime instanceof Date
});

// 模拟ensureTimeStringFormat处理
const processedCreateTime = mockOrder.createTime instanceof Date ? 
  mockOrder.createTime.toISOString() : mockOrder.createTime;

console.log('ensureTimeStringFormat处理后:', {
  createTime: processedCreateTime,
  type: typeof processedCreateTime
});

// 模拟订单卡片接收到的数据
const result = testOrderCardFormatTime(processedCreateTime);
console.log('订单卡片显示结果:', result);

// 6. 测试总结
console.log('\n📋 [修复总结]');
console.log('✅ 1. 修复了时区不一致问题：统一使用中国时区计算时间差');
console.log('✅ 2. 修复了Date对象处理：正确处理TimeZoneUtils返回的Date对象');
console.log('✅ 3. 修复了UTC字符串处理：正确转换UTC时间为中国时区显示');
console.log('✅ 4. 增强了错误处理：对无效时间返回"未知"');
console.log('✅ 5. 增加了调试信息：便于排查问题');

console.log('\n🎯 [预期效果]');
console.log('- 订单卡片时间不再随机显示"未知"');
console.log('- 刷新页面后时间显示保持一致');
console.log('- 时间显示准确反映中国时区的相对时间');
console.log('- 支持各种时间格式的正确处理');
