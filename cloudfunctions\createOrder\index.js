// 创建订单云函数
const cloud = require('wx-server-sdk');
const TimeZoneUtils = require('./common/timeZoneUtils');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 清理文本内容，去除多余空格和换行符
function cleanTextContent(text) {
  if (!text) return '';
  // 将换行符替换为空格，然后去除多余的空格
  return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();



  const {
    // 新的数据结构
    title,
    content,
    reward,
    platformType,
    duration,
    rounds,
    serviceType,
    tags,

    orderType,
    scheduledDate,
    scheduledTime,
    // 兼容旧的数据结构
    requirements,
    pricing,
    scheduleType
  } = event;

  console.log('解构后的数据:');
  console.log('服务类型:', serviceType);
  console.log('时长:', duration);
  console.log('局数:', rounds);
  console.log('标题:', title);
  console.log('内容:', content);
  console.log('奖励:', reward);
  console.log('🔍 [云函数] 平台类型:', platformType);
  console.log('🔍 [云函数] 平台类型类型:', typeof platformType);
  console.log('🔍 [云函数] 完整event对象:', JSON.stringify(event, null, 2));

  try {
    // 验证必填字段 - 支持新旧两种数据结构
    const isNewFormat = title && content && reward;
    const isOldFormat = serviceType && requirements && pricing;

    console.log('🔍 [云函数] 格式判断:');
    console.log('  - isNewFormat:', isNewFormat, '(title:', !!title, 'content:', !!content, 'reward:', !!reward, ')');
    console.log('  - isOldFormat:', isOldFormat, '(serviceType:', !!serviceType, 'requirements:', !!requirements, 'pricing:', !!pricing, ')');

    if (!isNewFormat && !isOldFormat) {
      return {
        success: false,
        error: '订单信息不完整'
      };
    }

    // 查找用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];

    // 计算订单金额
    let totalAmount;
    if (isNewFormat) {
      totalAmount = parseFloat(reward) || 0;
    } else {
      totalAmount = pricing.totalAmount || 0;
    }

    // 检查用户余额（暂时跳过余额检查，因为还没有实现支付系统）
    // if (user.balance < totalAmount) {
    //   return {
    //     success: false,
    //     error: '余额不足，请先充值'
    //   };
    // }

    // 简化为纯订单发布模式，任何用户都可以发布订单

    // 生成订单号
    const orderNo = generateOrderNo();

    // 构建订单数据 - 支持新旧两种格式
    let orderData;

    if (isNewFormat) {
      // 新格式：使用 title, content, reward 等字段
      orderData = {
        orderNo,
        customerId: user._id,
        customerOpenid: wxContext.OPENID, // 添加发布者的openid，用于权限判断
        accepterId: null, // 接单者ID，初始为空
        title: cleanTextContent(title),
        content: cleanTextContent(content),
        reward: totalAmount,
        platformType: platformType || 'pc', // 平台类型
        // 调试信息
        _debug_platformType: {
          original: platformType,
          final: platformType || 'pc',
          type: typeof platformType
        },
        // 服务类型和时间信息
        serviceType: serviceType || 'duration', // 默认为时长类型
        duration: serviceType === 'rounds' ? null : (duration || 1), // 局数类型时不设置时长
        rounds: serviceType === 'rounds' ? (rounds || 1) : null, // 局数信息
        tags: tags || [],
        orderType: orderType || 'immediate',
        status: 'pending', // 新订单都是待接单状态，可以在抢单大厅显示
        scheduledDate: scheduledDate || null,
        scheduledTime: scheduledTime || null,
        // 兼容字段
        requirements: cleanTextContent(content),
        pricing: {
          totalAmount: totalAmount,
          accepterAmount: totalAmount * 0.8, // 80%给接单者
          platformAmount: totalAmount * 0.2   // 20%平台费用
        },
        startTime: null,
        endTime: null,
        chatRoomId: null,
        evaluation: {
          customerRating: null,
          accepterRating: null,
          customerComment: '',
          accepterComment: ''
        },
        createTime: TimeZoneUtils.createStorageTime(),
        updateTime: TimeZoneUtils.createStorageTime()
      };
    } else {
      // 旧格式：兼容原有的数据结构
      orderData = {
        orderNo,
        customerId: user._id,
        customerOpenid: wxContext.OPENID, // 添加发布者的openid，用于权限判断
        accepterId: null,
        serviceType,
        requirements,
        pricing,
        platformType: platformType || 'pc', // 确保旧格式也包含平台类型
        status: 'pending', // 统一为待接单状态
        scheduleType,
        scheduledTime: scheduledTime ? new Date(scheduledTime) : null,
        startTime: null,
        endTime: null,
        chatRoomId: null,
        evaluation: {
          customerRating: null,
          accepterRating: null,
          customerComment: '',
          accepterComment: ''
        },
        createTime: TimeZoneUtils.createStorageTime(),
        updateTime: TimeZoneUtils.createStorageTime()
      };
    }

    console.log('🔍 [云函数] 准备保存的订单数据:', orderData);
    console.log('🔍 [云函数] 订单数据中的platformType:', orderData.platformType);

    const createResult = await db.collection('orders').add({
      data: orderData
    });

    console.log('订单创建成功:', {
      orderId: createResult._id,
      serviceType: orderData.serviceType,
      duration: orderData.duration,
      rounds: orderData.rounds,
      platformType: orderData.platformType,
      _debug_platformType: orderData._debug_platformType
    });
    console.log('🔍 [云函数] 完整订单数据:', JSON.stringify(orderData, null, 2));

    // 暂时跳过余额扣除和交易记录创建，因为还没有实现支付系统
    // 等支付系统完善后再启用这些功能

    // // 冻结用户余额
    // await db.collection('users').doc(user._id).update({
    //   data: {
    //     balance: db.command.inc(-totalAmount),
    //     updateTime: new Date()
    //   }
    // });

    // // 创建交易记录
    // await db.collection('transactions').add({
    //   data: {
    //     userId: user._id,
    //     orderId: createResult._id,
    //     type: 'payment',
    //     amount: -totalAmount,
    //     status: 'frozen',
    //     description: `订单支付 - ${orderNo}`,
    //     createTime: new Date()
    //   }
    // });

    // 发送新订单通知（广播给所有可接单用户）
    try {
      // 这里可以实现更复杂的通知逻辑，比如根据地理位置、技能匹配等
      // 暂时简化为日志记录
      console.log('新订单创建，订单ID:', createResult._id);

      // 可以在这里添加推送给附近用户的逻辑
      // 例如：查询附近的接单者并发送通知
    } catch (notificationError) {
      console.log('发送新订单通知失败:', notificationError);
      // 通知失败不影响订单创建流程
    }

    return {
      success: true,
      message: '订单创建成功',
      data: {
        orderId: createResult._id,
        orderNo
      }
    };
  } catch (error) {
    console.error('创建订单失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 生成订单号
function generateOrderNo() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `DT${year}${month}${day}${hour}${minute}${second}${random}`;
}
