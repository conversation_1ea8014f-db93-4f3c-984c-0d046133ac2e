// 公告系统测试脚本
// 在微信开发者工具的控制台中运行此脚本来测试公告功能

console.log('🧪 开始测试公告系统...');

// 测试数据
const testAnnouncements = [
  {
    title: '系统维护通知',
    content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用，请提前做好准备。维护完成后将为大家带来更好的使用体验！',
    type: 'system',
    priority: 1,
    status: 'active',
    publishTime: new Date(),
    effectiveTime: new Date(),
    expireTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
    isTop: true
  },
  {
    title: '新功能上线',
    content: '我们很高兴地宣布，全新的任务匹配系统已经上线！现在您可以更快速地找到合适的任务伙伴，提升游戏体验。',
    type: 'notice',
    priority: 2,
    status: 'active',
    publishTime: new Date(),
    effectiveTime: new Date(),
    expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
    isTop: false
  },
  {
    title: '紧急安全提醒',
    content: '近期发现有不法分子冒充平台客服进行诈骗，请大家提高警惕！平台客服不会主动要求您提供密码或转账，如有疑问请通过官方渠道联系我们。',
    type: 'urgent',
    priority: 1,
    status: 'active',
    publishTime: new Date(),
    effectiveTime: new Date(),
    expireTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3天后过期
    isTop: true
  }
];

// 测试函数
const testFunctions = {
  // 测试初始化数据库
  async testInitDatabase() {
    console.log('📊 测试初始化数据库...');
    try {
      const result = await wx.cloud.callFunction({
        name: 'initAnnouncementDatabase'
      });
      
      if (result.result && result.result.success) {
        console.log('✅ 数据库初始化成功');
        console.log('📝 创建的公告数量:', result.result.data.createdCount);
      } else {
        console.error('❌ 数据库初始化失败:', result.result?.error);
      }
    } catch (error) {
      console.error('❌ 数据库初始化异常:', error);
    }
  },

  // 测试创建公告
  async testCreateAnnouncement() {
    console.log('📝 测试创建公告...');
    
    for (let i = 0; i < testAnnouncements.length; i++) {
      const announcement = testAnnouncements[i];
      try {
        const result = await wx.cloud.callFunction({
          name: 'announcementManager',
          data: {
            action: 'create',
            ...announcement
          }
        });
        
        if (result.result && result.result.success) {
          console.log(`✅ 公告创建成功: ${announcement.title}`);
          console.log('📄 公告ID:', result.result.data._id);
        } else {
          console.error(`❌ 公告创建失败: ${announcement.title}`, result.result?.error);
        }
      } catch (error) {
        console.error(`❌ 公告创建异常: ${announcement.title}`, error);
      }
    }
  },

  // 测试获取公告列表
  async testGetAnnouncementList() {
    console.log('📋 测试获取公告列表...');
    try {
      const result = await wx.cloud.callFunction({
        name: 'getAnnouncementList',
        data: {
          page: 1,
          pageSize: 10,
          status: 'active',
          isAdmin: false
        }
      });
      
      if (result.result && result.result.success) {
        console.log('✅ 公告列表获取成功');
        console.log('📊 公告数量:', result.result.data.list.length);
        console.log('📄 公告列表:', result.result.data.list.map(item => ({
          id: item._id,
          title: item.title,
          type: item.type,
          status: item.status
        })));
      } else {
        console.error('❌ 公告列表获取失败:', result.result?.error);
      }
    } catch (error) {
      console.error('❌ 公告列表获取异常:', error);
    }
  },

  // 测试管理员获取公告列表
  async testGetAnnouncementListAdmin() {
    console.log('👨‍💼 测试管理员获取公告列表...');
    try {
      const result = await wx.cloud.callFunction({
        name: 'getAnnouncementList',
        data: {
          page: 1,
          pageSize: 10,
          isAdmin: true
        }
      });
      
      if (result.result && result.result.success) {
        console.log('✅ 管理员公告列表获取成功');
        console.log('📊 公告数量:', result.result.data.list.length);
        console.log('📄 公告列表:', result.result.data.list.map(item => ({
          id: item._id,
          title: item.title,
          type: item.type,
          status: item.status,
          viewCount: item.viewCount
        })));
      } else {
        console.error('❌ 管理员公告列表获取失败:', result.result?.error);
      }
    } catch (error) {
      console.error('❌ 管理员公告列表获取异常:', error);
    }
  },

  // 测试更新查看次数
  async testUpdateViewCount() {
    console.log('👁️ 测试更新查看次数...');
    
    // 先获取一个公告ID
    try {
      const listResult = await wx.cloud.callFunction({
        name: 'getAnnouncementList',
        data: { page: 1, pageSize: 1, isAdmin: true }
      });
      
      if (listResult.result?.success && listResult.result.data.list.length > 0) {
        const announcementId = listResult.result.data.list[0]._id;
        
        const result = await wx.cloud.callFunction({
          name: 'announcementManager',
          data: {
            action: 'updateViewCount',
            announcementId: announcementId
          }
        });
        
        if (result.result && result.result.success) {
          console.log('✅ 查看次数更新成功');
          console.log('📊 新的查看次数:', result.result.data.viewCount);
        } else {
          console.error('❌ 查看次数更新失败:', result.result?.error);
        }
      } else {
        console.log('⚠️ 没有找到可测试的公告');
      }
    } catch (error) {
      console.error('❌ 查看次数更新异常:', error);
    }
  },

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始运行所有测试...');
    console.log('='.repeat(50));
    
    await this.testInitDatabase();
    console.log('-'.repeat(30));
    
    await this.testCreateAnnouncement();
    console.log('-'.repeat(30));
    
    await this.testGetAnnouncementList();
    console.log('-'.repeat(30));
    
    await this.testGetAnnouncementListAdmin();
    console.log('-'.repeat(30));
    
    await this.testUpdateViewCount();
    console.log('-'.repeat(30));
    
    console.log('🎉 所有测试完成！');
    console.log('='.repeat(50));
  }
};

// 导出测试函数供控制台使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testFunctions;
} else {
  // 在浏览器环境中，将测试函数挂载到全局对象
  window.announcementTest = testFunctions;
}

console.log('🧪 公告系统测试脚本加载完成！');
console.log('💡 使用方法：');
console.log('  - 运行所有测试: announcementTest.runAllTests()');
console.log('  - 初始化数据库: announcementTest.testInitDatabase()');
console.log('  - 创建测试公告: announcementTest.testCreateAnnouncement()');
console.log('  - 获取公告列表: announcementTest.testGetAnnouncementList()');
console.log('  - 管理员获取列表: announcementTest.testGetAnnouncementListAdmin()');
console.log('  - 更新查看次数: announcementTest.testUpdateViewCount()');
