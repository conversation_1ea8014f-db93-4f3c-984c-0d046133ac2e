// 安全中间件云函数
const cloud = require('wx-server-sdk');
const TimeZoneUtils = require('./common/timeZoneUtils');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 安全配置
const SECURITY_CONFIG = {
  // 请求频率限制配置
  rateLimits: {
    'order.create': { maxRequests: 5, timeWindow: 60000 }, // 1分钟内最多创建5个订单
    'order.grab': { maxRequests: 20, timeWindow: 60000 },  // 1分钟内最多抢20个订单
    'message.send': { maxRequests: 100, timeWindow: 60000 }, // 1分钟内最多发送100条消息
    'evaluation.create': { maxRequests: 10, timeWindow: 60000 }, // 1分钟内最多创建10个评价
    'default': { maxRequests: 50, timeWindow: 60000 } // 默认限制
  },
  
  // 敏感字段配置
  sensitiveFields: ['phone', 'idCard', 'realName', 'address', 'email'],
  
  // 敏感词列表
  sensitiveWords: [
    '赌博', '色情', '暴力', '恐怖', '政治', '反动',
    '欺诈', '诈骗', '传销', '非法', '违法', '犯罪',
    '毒品', '枪支', '爆炸', '恐怖主义', '分裂',
    '邪教', '迷信', '封建', '反华', '台独', '藏独'
  ]
};

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action, data } = event;
  

  
  try {
    switch (action) {
      case 'checkRateLimit':
        return await checkRateLimit(data, wxContext);
      case 'validateInput':
        return await validateInput(data);
      case 'maskSensitiveData':
        return await maskSensitiveData(data);
      case 'checkPermission':
        return await checkPermission(data, wxContext);
      case 'logSecurityEvent':
        return await logSecurityEvent(data, wxContext);
      case 'getSecurityReport':
        return await getSecurityReport(data, wxContext);
      default:
        return {
          success: false,
          error: '未知的操作类型'
        };
    }
  } catch (error) {
    console.error('❌ [安全中间件] 操作失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 检查请求频率限制
async function checkRateLimit(data, wxContext) {
  const { operationType, customLimit } = data;
  const userId = wxContext.OPENID;

  try {
    // 获取限制配置
    const limitConfig = customLimit || SECURITY_CONFIG.rateLimits[operationType] || SECURITY_CONFIG.rateLimits.default;
    const { maxRequests, timeWindow } = limitConfig;
    
    // 查询用户最近的请求记录
    const cutoffTime = new Date(Date.now() - timeWindow);
    const requestsResult = await db.collection('securityLogs')
      .where({
        userId,
        operationType,
        createTime: _.gte(cutoffTime)
      })
      .count();
    
    const currentRequests = requestsResult.total;
    
    if (currentRequests >= maxRequests) {
      console.warn(`🚫 [频率限制] 用户 ${userId} 的 ${operationType} 操作超出限制`);
      
      // 记录违规事件
      await logSecurityEvent({
        eventType: 'RATE_LIMIT_EXCEEDED',
        userId,
        operationType,
        details: {
          currentRequests,
          maxRequests,
          timeWindow
        }
      }, { OPENID: userId });
      
      return {
        success: false,
        allowed: false,
        error: '操作过于频繁，请稍后再试',
        retryAfter: Math.ceil(timeWindow / 1000)
      };
    }
    
    // 记录本次请求
    await db.collection('securityLogs').add({
      data: {
        userId,
        operationType,
        eventType: 'REQUEST',
        createTime: new Date(),
        ipAddress: context.CLIENTIP || 'unknown',
        userAgent: context.CLIENTUA || 'unknown'
      }
    });
    
    console.log('✅ [频率检查] 通过，当前请求数:', currentRequests + 1);
    
    return {
      success: true,
      allowed: true,
      currentRequests: currentRequests + 1,
      maxRequests,
      remainingRequests: maxRequests - currentRequests - 1
    };
    
  } catch (error) {
    console.error('❌ [频率检查] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 输入验证
async function validateInput(data) {
  const { inputs } = data;
  
  console.log('🛡️ [输入验证] 验证字段数量:', Object.keys(inputs).length);
  
  try {
    const results = {};
    let hasError = false;
    
    for (const [fieldName, fieldData] of Object.entries(inputs)) {
      const { value, type, options = {} } = fieldData;
      const result = validateField(value, type, options);
      
      results[fieldName] = result;
      if (!result.valid) {
        hasError = true;
      }
    }
    
    console.log('✅ [输入验证] 完成，有错误:', hasError);
    
    return {
      success: true,
      valid: !hasError,
      results
    };
    
  } catch (error) {
    console.error('❌ [输入验证] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 单个字段验证
function validateField(value, type, options) {
  if (value === null || value === undefined || value === '') {
    if (options.required) {
      return { valid: false, error: '此字段为必填项' };
    }
    return { valid: true, value };
  }

  const stringValue = String(value).trim();
  
  switch (type) {
    case 'phone':
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(stringValue)) {
        return { valid: false, error: '手机号格式不正确' };
      }
      break;
    
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(stringValue)) {
        return { valid: false, error: '邮箱格式不正确' };
      }
      break;
    
    case 'text':
      const minLength = options.minLength || 0;
      const maxLength = options.maxLength || 1000;
      
      if (stringValue.length < minLength) {
        return { valid: false, error: `文本长度不能少于${minLength}个字符` };
      }
      if (stringValue.length > maxLength) {
        return { valid: false, error: `文本长度不能超过${maxLength}个字符` };
      }
      
      // 敏感词检查
      if (containsSensitiveWords(stringValue)) {
        return { valid: false, error: '内容包含敏感词汇' };
      }
      break;
    
    case 'number':
      const num = Number(stringValue);
      if (isNaN(num)) {
        return { valid: false, error: '必须是有效数字' };
      }
      
      const min = options.min !== undefined ? options.min : -Infinity;
      const max = options.max !== undefined ? options.max : Infinity;
      
      if (num < min || num > max) {
        return { valid: false, error: `数值必须在${min}到${max}之间` };
      }
      break;
    
    case 'url':
      try {
        new URL(stringValue);
      } catch {
        return { valid: false, error: 'URL格式不正确' };
      }
      break;
    
    default:
      break;
  }

  return { valid: true, value: stringValue };
}

// 敏感词检查
function containsSensitiveWords(text) {
  const lowerText = text.toLowerCase();
  return SECURITY_CONFIG.sensitiveWords.some(word => lowerText.includes(word.toLowerCase()));
}

// 数据脱敏
async function maskSensitiveData(data) {
  const { dataObject, fieldsToMask } = data;
  

  
  try {
    const fieldsToProcess = fieldsToMask || SECURITY_CONFIG.sensitiveFields;
    const maskedData = { ...dataObject };
    
    for (const field of fieldsToProcess) {
      if (maskedData[field]) {
        maskedData[field] = maskField(maskedData[field], field);
      }
    }
    

    
    return {
      success: true,
      data: maskedData
    };
    
  } catch (error) {
    console.error('❌ [数据脱敏] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 字段脱敏处理
function maskField(value, fieldType) {
  if (!value || typeof value !== 'string') {
    return value;
  }

  switch (fieldType) {
    case 'phone':
      return value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    case 'idCard':
      return value.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
    case 'realName':
      if (value.length <= 2) {
        return value.charAt(0) + '*';
      }
      return value.charAt(0) + '*'.repeat(value.length - 2) + value.charAt(value.length - 1);
    case 'address':
      if (value.length <= 6) {
        return value.substring(0, 2) + '*'.repeat(value.length - 2);
      }
      return value.substring(0, 6) + '*'.repeat(Math.min(value.length - 6, 10));
    case 'email':
      const [username, domain] = value.split('@');
      if (username.length <= 3) {
        return username.charAt(0) + '***@' + domain;
      }
      return username.substring(0, 3) + '***@' + domain;
    default:
      return value;
  }
}

// 权限检查
async function checkPermission(data, wxContext) {
  const { permission, resourceId, resourceType } = data;
  const userId = wxContext.OPENID;
  
  console.log('🛡️ [权限检查] 用户:', userId, '权限:', permission, '资源:', resourceId);
  
  try {
    // 检查用户是否存在且未被封禁
    const userResult = await db.collection('users')
      .where({ _openid: userId })
      .get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        authorized: false,
        error: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    if (user.status === 'blocked') {
      return {
        success: false,
        authorized: false,
        error: '用户已被封禁'
      };
    }
    
    // 根据权限类型进行具体检查
    let authorized = true;
    let errorMessage = '';
    
    switch (permission) {
      case 'order.view':
      case 'order.modify':
        if (resourceId) {
          const orderResult = await db.collection('orders').doc(resourceId).get();
          if (orderResult.data) {
            const order = orderResult.data;
            const isOwner = order.customerId === userId;
            const isAccepter = order.accepterId === userId;
            
            if (permission === 'order.view') {
              authorized = isOwner || isAccepter;
            } else if (permission === 'order.modify') {
              authorized = isOwner && order.status === '待接单';
            }
            
            if (!authorized) {
              errorMessage = '无权限访问此订单';
            }
          } else {
            authorized = false;
            errorMessage = '订单不存在';
          }
        }
        break;
      
      case 'chat.access':
        if (resourceId) {
          const chatResult = await db.collection('chatRooms').doc(resourceId).get();
          if (chatResult.data) {
            const chatRoom = chatResult.data;
            authorized = chatRoom.customerId === userId || chatRoom.accepterId === userId;
            if (!authorized) {
              errorMessage = '无权限访问此聊天室';
            }
          } else {
            authorized = false;
            errorMessage = '聊天室不存在';
          }
        }
        break;
      
      default:
        // 默认允许
        break;
    }
    
    console.log('✅ [权限检查] 结果:', authorized);
    
    return {
      success: true,
      authorized,
      error: errorMessage || null
    };
    
  } catch (error) {
    console.error('❌ [权限检查] 失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 记录安全事件
async function logSecurityEvent(data, wxContext) {
  const { eventType, operationType, details, severity = 'medium' } = data;
  const userId = wxContext.OPENID;
  

  
  try {
    await db.collection('securityLogs').add({
      data: {
        userId,
        eventType,
        operationType: operationType || 'unknown',
        severity,
        details: details || {},
        createTime: new Date(),
        ipAddress: context.CLIENTIP || 'unknown',
        userAgent: context.CLIENTUA || 'unknown'
      }
    });
    

    
    return {
      success: true,
      message: '安全事件已记录'
    };
    
  } catch (error) {
    console.error('❌ [安全日志] 记录失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 获取安全报告
async function getSecurityReport(data, wxContext) {
  const { timeRange = 'day', eventTypes } = data;
  

  
  try {
    // 计算时间范围（使用中国时区）
    let startTime;
    const chinaTime = TimeZoneUtils.getChinaTime();

    switch (timeRange) {
      case 'hour':
        startTime = new Date(chinaTime.getTime() - 60 * 60 * 1000);
        break;
      case 'day':
        startTime = new Date(chinaTime.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startTime = new Date(chinaTime.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startTime = new Date(chinaTime.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(chinaTime.getTime() - 24 * 60 * 60 * 1000);
    }

    // 转换为UTC时间用于数据库查询
    startTime = TimeZoneUtils.chinaTimeToUTC(startTime);
    
    // 构建查询条件
    let whereCondition = {
      createTime: _.gte(startTime)
    };
    
    if (eventTypes && eventTypes.length > 0) {
      whereCondition.eventType = _.in(eventTypes);
    }
    
    // 查询安全日志
    const logsResult = await db.collection('securityLogs')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .limit(1000)
      .get();
    
    // 统计分析
    const logs = logsResult.data;
    const report = {
      totalEvents: logs.length,
      timeRange,
      startTime,
      endTime: now,
      eventStats: {},
      severityStats: {},
      topUsers: {},
      recentEvents: logs.slice(0, 20)
    };
    
    // 统计事件类型
    logs.forEach(log => {
      report.eventStats[log.eventType] = (report.eventStats[log.eventType] || 0) + 1;
      report.severityStats[log.severity] = (report.severityStats[log.severity] || 0) + 1;
      report.topUsers[log.userId] = (report.topUsers[log.userId] || 0) + 1;
    });
    

    
    return {
      success: true,
      data: report
    };
    
  } catch (error) {
    console.error('❌ [安全报告] 生成失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
