<!-- 公告详情页面 -->
<navigation-bar title="公告详情" back="{{true}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)"></navigation-bar>

<!-- 科技感装饰背景 -->
<view class="tech-bg">
  <view class="tech-grid"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3]}}" wx:key="*this"></view>
  </view>
</view>

<scroll-view class="scrollarea page-with-custom-nav" scroll-y type="list">
  <view class="container" wx:if="{{announcement}}">
    <!-- 公告卡片 -->
    <view class="announcement-card fade-in">
      <!-- 公告头部 -->
      <view class="announcement-header">
        <view class="announcement-type {{announcement.type}}" wx:if="{{announcement.type === 'urgent'}}">
          <text class="type-icon">⚠️</text>
          <text class="type-text">紧急公告</text>
        </view>
        <view class="announcement-type {{announcement.type}}" wx:elif="{{announcement.type === 'system'}}">
          <text class="type-icon">🔧</text>
          <text class="type-text">系统公告</text>
        </view>
        <view class="announcement-type {{announcement.type}}" wx:else>
          <text class="type-icon">📢</text>
          <text class="type-text">平台公告</text>
        </view>
      </view>

      <!-- 公告标题 -->
      <view class="announcement-title">{{announcement.title}}</view>

      <!-- 公告元信息 -->
      <view class="announcement-meta">
        <view class="meta-item">
          <text class="meta-label">发布时间</text>
          <text class="meta-value">{{announcement.publishTimeFormatted}}</text>
        </view>
        <view class="meta-item" wx:if="{{announcement.expireTimeFormatted}}">
          <text class="meta-label">有效期至</text>
          <text class="meta-value">{{announcement.expireTimeFormatted}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-label">查看次数</text>
          <text class="meta-value">{{announcement.viewCount || 0}}</text>
        </view>
      </view>

      <!-- 公告内容 -->
      <view class="announcement-content">
        <text class="content-text">{{announcement.content}}</text>
      </view>

      <!-- 公告状态 -->
      <view class="announcement-status" wx:if="{{announcement.isExpired}}">
        <text class="status-icon">⏰</text>
        <text class="status-text">此公告已过期</text>
      </view>
    </view>

    <!-- 相关公告 -->
    <view class="related-announcements fade-in" style="animation-delay: 0.3s;" wx:if="{{relatedAnnouncements.length > 0}}">
      <view class="section-title">
        <text class="title-icon">📋</text>
        <text class="title-text">相关公告</text>
      </view>

      <view class="related-list">
        <view 
          class="related-item"
          wx:for="{{relatedAnnouncements}}"
          wx:key="_id"
          bindtap="onRelatedAnnouncementTap"
          data-id="{{item._id}}">
          
          <view class="related-content">
            <text class="related-title">{{item.title}}</text>
            <text class="related-time">{{item.publishTimeFormatted}}</text>
          </view>
          
          <view class="related-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{error}}">
    <text class="error-icon">❌</text>
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="loadAnnouncementDetail">重试</button>
  </view>
</scroll-view>
