// 测试订单实时监听的数据流
// 在微信开发者工具的控制台中运行此脚本

console.log('🧪 开始测试订单实时监听数据流...');

// 模拟订单数据（基于createOrder云函数的返回格式）
const mockOrderData = {
  _id: "test_order_" + Date.now(),
  orderNo: "ORD" + Date.now(),
  customerId: "test_customer_id",
  customerOpenid: "test_customer_openid_123", // 这是关键字段
  accepterId: null,
  title: "测试订单标题",
  content: "测试订单内容",
  reward: 50,
  platformType: "mobile",
  serviceType: "duration",
  duration: 2,
  rounds: null,
  tags: ["测试"],
  orderType: "immediate",
  status: "pending",
  scheduledDate: null,
  scheduledTime: null,
  chatRoomId: null,
  evaluation: {
    customerRating: null,
    accepterRating: null,
    customerComment: '',
    accepterComment: ''
  },
  createTime: new Date(),
  updateTime: new Date()
};

console.log('📦 模拟订单数据:', mockOrderData);

// 测试formatNewOrderDataInstantly方法
function testFormatNewOrderDataInstantly(order) {
  console.log('🔍 [测试] 开始格式化订单数据...');
  
  // 模拟customerInfo处理逻辑
  const customerInfo = order.customerInfo || {
    nickName: '未知用户',
    avatarUrl: '',
    _id: order.customerId,
    openid: order.customerOpenid || order._openid || ''
  };
  
  console.log('🔍 [测试] 处理后的customerInfo:', customerInfo);
  
  // 模拟权限判断
  const mockCurrentUser = {
    openid: "test_customer_openid_123" // 假设是同一个用户
  };
  
  const isOwner = mockCurrentUser.openid === customerInfo.openid;
  console.log('🔍 [测试] 权限判断结果:', {
    currentUserOpenid: mockCurrentUser.openid,
    customerOpenid: customerInfo.openid,
    isOwner: isOwner
  });
  
  const result = {
    ...order,
    customerInfo: customerInfo,
    isOwner: isOwner,
    canGrab: !isOwner,
    isGrabOrder: !isOwner
  };
  
  console.log('✅ [测试] 格式化结果:', result);
  return result;
}

// 执行测试
const formattedOrder = testFormatNewOrderDataInstantly(mockOrderData);

// 测试异步权限更新逻辑
function testAsyncPermissionUpdate(orderId, customerId) {
  console.log('🔍 [测试] 模拟异步权限更新...');
  
  // 模拟数据库查询结果
  const mockUserData = {
    _id: customerId,
    openid: "test_customer_openid_123",
    nickName: "测试用户",
    avatarUrl: "https://example.com/avatar.jpg"
  };
  
  console.log('🔍 [测试] 模拟查询到的用户数据:', mockUserData);
  
  // 模拟当前用户
  const mockCurrentUser = {
    openid: "test_customer_openid_123"
  };
  
  const isOwner = mockCurrentUser.openid === mockUserData.openid;
  
  console.log('🔍 [测试] 异步权限更新结果:', {
    orderId,
    currentUserOpenid: mockCurrentUser.openid,
    customerOpenid: mockUserData.openid,
    isOwner: isOwner
  });
  
  return {
    customerInfo: {
      _id: mockUserData._id,
      nickName: mockUserData.nickName,
      avatarUrl: mockUserData.avatarUrl,
      openid: mockUserData.openid
    },
    isOwner: isOwner,
    canGrab: !isOwner,
    isGrabOrder: !isOwner
  };
}

// 执行异步权限更新测试
const asyncResult = testAsyncPermissionUpdate(mockOrderData._id, mockOrderData.customerId);

console.log('🎯 [测试总结]');
console.log('1. 订单数据包含customerOpenid:', !!mockOrderData.customerOpenid);
console.log('2. 格式化后customerInfo包含openid:', !!formattedOrder.customerInfo.openid);
console.log('3. 权限判断正确:', formattedOrder.isOwner);
console.log('4. 异步更新正确:', asyncResult.isOwner);

// 检查可能的问题
console.log('🔍 [问题排查]');
if (!mockOrderData.customerOpenid) {
  console.error('❌ 订单数据缺少customerOpenid字段');
}
if (!formattedOrder.customerInfo.openid) {
  console.error('❌ 格式化后customerInfo缺少openid字段');
}
if (formattedOrder.customerInfo.nickName === '未知用户') {
  console.warn('⚠️ 用户显示为"未知用户"，可能需要异步更新');
}

console.log('✅ 测试完成！');

// 实际使用建议
console.log('📋 [实际使用建议]');
console.log('1. 确保createOrder云函数返回完整的customerOpenid');
console.log('2. 在formatNewOrderDataInstantly中正确提取openid');
console.log('3. 如果openid缺失，触发异步更新');
console.log('4. 监控实时监听的数据完整性');
console.log('5. 首页监听应该只监听pending状态的订单');

// 测试实际的数据库查询
console.log('🔍 [数据库查询测试]');
console.log('建议在微信开发者工具控制台中运行以下代码来测试实际数据：');
console.log(`
// 测试订单数据完整性
const db = wx.cloud.database();
db.collection('orders')
  .where({ status: 'pending' })
  .orderBy('createTime', 'desc')
  .limit(5)
  .get()
  .then(res => {
    console.log('📦 实际订单数据:', res.data);
    res.data.forEach(order => {
      console.log('🔍 订单', order._id, '的用户信息:', {
        customerId: order.customerId,
        customerOpenid: order.customerOpenid,
        hasCustomerInfo: !!order.customerInfo,
        customerInfoOpenid: order.customerInfo?.openid
      });
    });
  })
  .catch(err => {
    console.error('❌ 查询失败:', err);
  });
`);

// 测试用户信息查询
console.log('🔍 [用户信息查询测试]');
console.log('测试用户信息查询的代码：');
console.log(`
// 测试用户信息查询
const testUserId = 'your_user_id_here'; // 替换为实际的用户ID
db.collection('users')
  .where({ _id: testUserId })
  .field({ _id: true, openid: true, nickName: true, avatarUrl: true })
  .get()
  .then(res => {
    console.log('👤 用户信息查询结果:', res.data);
    if (res.data.length > 0) {
      const user = res.data[0];
      console.log('✅ 用户信息完整:', {
        hasOpenid: !!user.openid,
        hasNickName: !!user.nickName,
        hasAvatarUrl: !!user.avatarUrl
      });
    } else {
      console.log('❌ 未找到用户信息');
    }
  })
  .catch(err => {
    console.error('❌ 用户查询失败:', err);
  });
`);
