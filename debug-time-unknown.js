// 调试时间显示"未知"问题的综合脚本
// 在微信开发者工具的控制台中运行此脚本

console.log('🔍 开始全面调试时间显示"未知"问题...');

// 1. 检查当前首页订单数据
function checkCurrentOrdersData() {
  console.log('\n📋 [检查] 当前首页订单数据:');
  
  // 获取当前页面实例
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  
  if (!currentPage || !currentPage.data) {
    console.log('❌ 无法获取当前页面数据');
    return;
  }
  
  const orders = currentPage.data.latestOrders || [];
  console.log(`📦 当前订单数量: ${orders.length}`);
  
  orders.forEach((order, index) => {
    console.log(`\n订单 ${index + 1}:`, {
      id: order._id,
      title: order.title,
      createTime: order.createTime,
      createTimeType: typeof order.createTime,
      isDate: order.createTime instanceof Date,
      createTimeText: order.createTimeText,
      formattedCreateTime: order.formattedCreateTime
    });
    
    // 测试时间格式化
    const testResult = testTimeFormatting(order.createTime);
    console.log(`时间格式化测试结果: ${testResult}`);
  });
}

// 2. 测试时间格式化函数
function testTimeFormatting(dateStr) {
  if (!dateStr) {
    return '未知 (空值)';
  }

  // 处理Date对象和字符串两种情况
  const date = dateStr instanceof Date ? dateStr : new Date(dateStr);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date:', dateStr);
    return '未知 (无效日期)';
  }

  const now = new Date();
  const diff = now - date;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`;
  } else {
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${month}-${day}`;
  }
}

// 3. 模拟各种可能的时间数据格式
function testVariousTimeFormats() {
  console.log('\n🧪 [测试] 各种时间格式:');
  
  const testCases = [
    { name: 'Date对象', value: new Date() },
    { name: 'ISO字符串', value: new Date().toISOString() },
    { name: '时间戳数字', value: Date.now() },
    { name: '时间戳字符串', value: Date.now().toString() },
    { name: '空对象', value: {} },
    { name: '空数组', value: [] },
    { name: 'null', value: null },
    { name: 'undefined', value: undefined },
    { name: '空字符串', value: '' },
    { name: '无效字符串', value: 'invalid-date' },
    { name: '序列化后的Date', value: JSON.parse(JSON.stringify({ date: new Date() })).date }
  ];
  
  testCases.forEach(testCase => {
    const result = testTimeFormatting(testCase.value);
    console.log(`${testCase.name}: ${JSON.stringify(testCase.value)} → ${result}`);
  });
}

// 4. 检查订单卡片组件的时间处理
function checkOrderCardTimeHandling() {
  console.log('\n🎯 [检查] 订单卡片组件时间处理:');
  
  // 查找页面中的订单卡片组件
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  
  if (!currentPage) {
    console.log('❌ 无法获取当前页面');
    return;
  }
  
  // 模拟订单卡片的formatTime方法
  function mockOrderCardFormatTime(dateStr) {
    console.log('🕐 [订单卡片模拟] 输入:', {
      value: dateStr,
      type: typeof dateStr,
      isDate: dateStr instanceof Date
    });
    
    if (!dateStr) {
      console.log('🕐 [订单卡片模拟] 返回: 未知 (空值)');
      return '未知';
    }

    // 处理Date对象和字符串两种情况
    const date = dateStr instanceof Date ? dateStr : new Date(dateStr);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('🕐 [订单卡片模拟] Invalid date:', dateStr);
      return '未知';
    }

    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${month}-${day}`;
    }
  }
  
  // 测试当前订单的时间处理
  const orders = currentPage.data.latestOrders || [];
  orders.forEach((order, index) => {
    console.log(`\n测试订单 ${index + 1} 的时间处理:`);
    const result = mockOrderCardFormatTime(order.createTime);
    console.log(`结果: ${result}`);
  });
}

// 5. 监控setData调用
function monitorSetDataCalls() {
  console.log('\n👀 [监控] 开始监控setData调用...');
  
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  
  if (!currentPage) {
    console.log('❌ 无法获取当前页面');
    return;
  }
  
  // 保存原始setData方法
  const originalSetData = currentPage.setData;
  
  // 包装setData方法
  currentPage.setData = function(data, callback) {
    console.log('📡 [setData监控] 调用setData:', data);
    
    // 检查是否包含latestOrders
    if (data.latestOrders) {
      console.log('📡 [setData监控] 更新latestOrders:');
      data.latestOrders.forEach((order, index) => {
        console.log(`订单 ${index + 1}:`, {
          id: order._id,
          createTime: order.createTime,
          createTimeType: typeof order.createTime,
          isDate: order.createTime instanceof Date
        });
      });
    }
    
    // 调用原始方法
    return originalSetData.call(this, data, callback);
  };
  
  console.log('✅ setData监控已启动');
  
  // 5分钟后自动停止监控
  setTimeout(() => {
    currentPage.setData = originalSetData;
    console.log('⏰ setData监控已停止');
  }, 5 * 60 * 1000);
}

// 6. 检查实时监听数据流
function checkRealtimeDataFlow() {
  console.log('\n🔄 [检查] 实时监听数据流:');
  
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  
  if (!currentPage) {
    console.log('❌ 无法获取当前页面');
    return;
  }
  
  // 检查实时监听状态
  console.log('实时监听状态:', {
    isWatcherActive: currentPage.data.isWatcherActive,
    hasOrderWatcher: !!currentPage.data.orderWatcher
  });
  
  // 模拟实时监听数据处理
  if (currentPage.formatNewOrderDataInstantly) {
    console.log('✅ formatNewOrderDataInstantly 方法存在');
  } else {
    console.log('❌ formatNewOrderDataInstantly 方法不存在');
  }
}

// 7. 主要检查函数
function runFullDiagnostic() {
  console.log('🚀 开始全面诊断...\n');
  
  checkCurrentOrdersData();
  testVariousTimeFormats();
  checkOrderCardTimeHandling();
  checkRealtimeDataFlow();
  
  console.log('\n💡 [建议] 如果问题仍然存在:');
  console.log('   1. 运行 monitorSetDataCalls() 监控数据更新');
  console.log('   2. 发布一个新订单，观察控制台输出');
  console.log('   3. 在首页和我的订单之间切换，观察数据变化');
  console.log('   4. 检查是否有其他地方直接修改了订单数据');
}

// 8. 快速修复测试
function quickFixTest() {
  console.log('\n🔧 [快速修复] 测试时间字段修复:');
  
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  
  if (!currentPage || !currentPage.data.latestOrders) {
    console.log('❌ 无法获取订单数据');
    return;
  }
  
  const orders = currentPage.data.latestOrders;
  const fixedOrders = orders.map(order => ({
    ...order,
    createTime: order.createTime instanceof Date ? order.createTime.toISOString() : order.createTime,
    updateTime: order.updateTime instanceof Date ? order.updateTime.toISOString() : order.updateTime
  }));
  
  console.log('🔧 应用时间字段修复...');
  currentPage.setData({
    latestOrders: fixedOrders
  });
  
  console.log('✅ 修复完成，请检查页面显示');
}

// 导出函数供控制台使用
window.debugTimeUnknown = {
  runFullDiagnostic,
  checkCurrentOrdersData,
  testVariousTimeFormats,
  checkOrderCardTimeHandling,
  monitorSetDataCalls,
  checkRealtimeDataFlow,
  quickFixTest,
  testTimeFormatting
};

console.log('\n🎯 [使用方法]:');
console.log('   debugTimeUnknown.runFullDiagnostic() - 运行全面诊断');
console.log('   debugTimeUnknown.monitorSetDataCalls() - 监控setData调用');
console.log('   debugTimeUnknown.quickFixTest() - 快速修复测试');

// 自动运行诊断
runFullDiagnostic();
