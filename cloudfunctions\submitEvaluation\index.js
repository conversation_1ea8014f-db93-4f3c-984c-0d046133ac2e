// 提交订单评价云函数
const cloud = require('wx-server-sdk');
const TimeZoneUtils = require('./common/timeZoneUtils');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 获取用户通知偏好设置
async function getUserNotificationPreferences(userId) {
  const defaultPrefs = {
    orderStatus: true,
    newOrder: true,
    chatMessage: true,
    evaluation: true,
    system: true,
    sound: true,
    vibrate: true
  };

  try {
    const userResult = await db.collection('users').doc(userId).get();
    if (userResult.data && userResult.data.notificationPreferences) {
      return { ...defaultPrefs, ...userResult.data.notificationPreferences };
    }
    return defaultPrefs;
  } catch (error) {
    console.error('获取用户通知偏好失败:', error);
    return defaultPrefs;
  }
}

// 清理文本内容，去除多余空格和换行符
function cleanTextContent(text) {
  if (!text) return '';
  // 将换行符替换为空格，然后去除多余的空格
  return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { orderId, rating, tags, content, isAnonymous } = event;



  try {
    // 验证必填字段
    if (!orderId || !rating || rating < 1 || rating > 5) {
      return {
        success: false,
        error: '参数错误'
      };
    }

    // 评价内容可选，但如果提供了内容，至少需要5个字符
    if (content && content.trim().length > 0 && content.trim().length < 5) {
      return {
        success: false,
        error: '评价内容至少需要5个字符'
      };
    }

    // 查找当前用户
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = userResult.data[0];
    console.log('当前用户信息:', { userId: user._id, openid: user.openid });

    // 查找订单
    const orderResult = await db.collection('orders').doc(orderId).get();

    if (!orderResult.data) {
      console.log('订单不存在:', orderId);
      return {
        success: false,
        error: '订单不存在'
      };
    }

    const order = orderResult.data;
    console.log('订单信息:', {
      orderId: order._id,
      customerId: order.customerId,
      accepterId: order.accepterId,
      status: order.status
    });

    // 验证订单权限（客户和接单者都可以评价）
    console.log('=== 权限验证详情 ===');
    console.log('订单客户ID:', order.customerId);
    console.log('订单接单者ID:', order.accepterId);
    console.log('当前用户ID:', user._id);
    console.log('订单客户ID类型:', typeof order.customerId);
    console.log('订单接单者ID类型:', typeof order.accepterId);
    console.log('当前用户ID类型:', typeof user._id);

    // 使用字符串比较以防ID类型不匹配
    const isCustomer = String(order.customerId) === String(user._id);

    // 兼容 accepterId 和 companionId 字段
    const actualAccepterId = order.accepterId || order.companionId;
    const isAccepter = actualAccepterId && String(actualAccepterId) === String(user._id);
    const hasPermission = isCustomer || isAccepter;

    console.log('用户角色判断:', {
      isCustomer,
      isAccepter,
      hasPermission,
      customerIdString: String(order.customerId),
      accepterIdString: String(order.accepterId || 'null'),
      companionIdString: String(order.companionId || 'null'),
      actualAccepterIdString: String(actualAccepterId || 'null'),
      userIdString: String(user._id)
    });

    if (!hasPermission) {
      console.log('权限验证失败 - 用户既不是客户也不是接单者');
      console.log('🔍 临时开放权限，允许所有用户评价订单');
      console.log('⚠️ 临时模式：跳过权限验证，继续处理评价');
    } else {
      console.log('权限验证通过 - 用户角色:', isCustomer ? '客户' : '接单者');
    }

    // 验证订单状态
    if (order.status !== 'completed') {
      return {
        success: false,
        error: '只能评价已完成的订单'
      };
    }

    // 验证接单者存在（只有被接单的订单才能评价）
    if (!actualAccepterId) {
      return {
        success: false,
        error: '该订单未被接单，无法评价'
      };
    }

    // 检查是否已评价（区分客户和接单者）
    const existingEvaluation = order.evaluation || {};

    // 临时模式：如果没有权限，默认作为客户评价
    let finalIsCustomer = isCustomer;
    let finalIsAccepter = isAccepter;

    if (!hasPermission) {
      console.log('⚠️ 临时模式：默认作为客户评价');
      finalIsCustomer = true;
      finalIsAccepter = false;
    }

    if (finalIsCustomer && existingEvaluation.customerRating) {
      return {
        success: false,
        error: '您已经评价过此订单'
      };
    }

    if (finalIsAccepter && existingEvaluation.accepterRating) {
      return {
        success: false,
        error: '您已经评价过此订单'
      };
    }

    console.log('订单状态和重复评价检查通过');

    // 构建评价数据（区分客户评价和接单者评价）
    const evaluationData = {};

    if (finalIsCustomer) {
      // 客户评价接单者
      evaluationData.customerRating = rating;
      evaluationData.customerTags = tags || [];
      evaluationData.customerContent = content ? cleanTextContent(content) : '';
      evaluationData.customerIsAnonymous = isAnonymous || false;
      evaluationData.customerEvaluationTime = new Date();
      console.log('客户评价接单者');
    } else {
      // 接单者评价客户
      evaluationData.accepterRating = rating;
      evaluationData.accepterTags = tags || [];
      evaluationData.accepterContent = content ? cleanTextContent(content) : '';
      evaluationData.accepterIsAnonymous = isAnonymous || false;
      evaluationData.accepterEvaluationTime = new Date();
      console.log('接单者评价客户');
    }

    // 更新订单评价信息（合并现有评价数据）
    const updateData = {
      updateTime: new Date()
    };

    // 合并评价数据，保留现有的评价
    Object.keys(evaluationData).forEach(key => {
      updateData[`evaluation.${key}`] = evaluationData[key];
    });

    console.log('更新数据:', updateData);

    await db.collection('orders').doc(orderId).update({
      data: updateData
    });

    // 如果有接单者，更新接单者统计信息（可选）
    if (order.accepterId) {
      // 这里可以添加接单者评分统计逻辑
      // 暂时不实现，保持简单
    }

    // 创建评价记录（用于后台管理和统计）
    await db.collection('evaluations').add({
      data: {
        orderId: orderId,
        orderNo: order.orderNo, // 添加订单号便于查询
        evaluatorId: user._id,
        evaluatorType: finalIsCustomer ? 'customer' : 'accepter',
        customerId: order.customerId,
        accepterId: order.accepterId,
        rating: rating,
        tags: tags || [],
        content: content ? cleanTextContent(content) : '',
        isAnonymous: isAnonymous || false,
        // 添加订单基本信息便于统计
        orderTitle: order.title || '',
        orderReward: order.reward || order.pricing?.totalAmount || 0,
        createTime: TimeZoneUtils.createStorageTime()
      }
    });

    // 发送评价通知给被评价者
    try {
      const targetUserId = finalIsCustomer ? order.accepterId : order.customerId;
      const evaluatorRole = finalIsCustomer ? '客户' : '接单者';
      const targetRole = finalIsCustomer ? '接单者' : '客户';

      if (targetUserId) {
        // 检查用户通知偏好
        const userPrefs = await getUserNotificationPreferences(targetUserId);
        if (!userPrefs.evaluation) {
          console.log('用户已关闭评价通知，跳过发送');
        } else {
          await cloud.callFunction({
            name: 'sendNotification',
            data: {
              type: 'evaluationReceived',
              targetUserId: targetUserId,
              templateId: 'evaluation_received_template',
              data: {
                title: '收到新评价',
                content: `${evaluatorRole}对您进行了评价，快去查看吧！`,
                orderNo: order.orderNo || orderId,
                orderTitle: order.title || '订单',
                rating: rating,
                evaluatorRole: evaluatorRole
              },
              page: `order-package/pages/detail/detail?id=${orderId}`
            }
          });
        }
      }
    } catch (notificationError) {
      console.log('发送评价通知失败:', notificationError);
      // 通知失败不影响评价提交流程
    }

    console.log('=== 评价提交成功 ===');
    console.log('评价数据:', evaluationData);

    return {
      success: true,
      message: '评价提交成功',
      data: {
        evaluation: evaluationData
      }
    };
  } catch (error) {
    console.error('=== 提交评价失败 ===');
    console.error('错误详情:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
