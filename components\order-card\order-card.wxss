/* 三角洲行动战术任务订单卡片样式 */
.order-card {
  width: 100%;
  max-width: 700rpx;
  height: 280rpx !important; /* 确保高度不被覆盖 */
  min-height: 280rpx; /* 设置最小高度 */
  background: linear-gradient(to right, #1e2d3d 0%, #15202b 100%);
  border-radius: 16rpx;
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.6);
  overflow: hidden;
  /* 移除深色边框 */
  display: flex;
  position: relative;
  margin-bottom: 20rpx;
}

/* 左侧装饰条 */
.tactical-strip {
  width: 8rpx;
  background: linear-gradient(to bottom, #4ab37e, #e2b93b);
}

/* 信息展示区 */
.info-panel {
  flex: 1;
  padding: 20rpx 20rpx 16rpx 20rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto auto 1fr auto;
  gap: 8rpx;
}

/* 顶部标题区域 */
.card-header {
  grid-column: 1 / 3;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4rpx;
  position: relative;
  min-height: 40rpx;
  height: 40rpx;
}

.mission-title {
  font-size: 28rpx;
  font-weight: 800;
  color: #e2b93b;
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
  min-height: 40rpx;
  overflow: hidden;
}

.mission-title .title-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.skull-icon {
  font-size: 28rpx;
}

.mission-price {
  background: linear-gradient(to right, #e2b93b, #d9a326);
  color: #1a1a1a;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-weight: 800;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(226, 185, 59, 0.3);
  z-index: 2;
}



/* 信息项 */
.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 20rpx;
}

.info-icon {
  color: #4ab37e;
  width: 24rpx;
  text-align: center;
  font-size: 20rpx;
}

.info-label {
  color: #8db39c;
  min-width: 60rpx;
}

.info-value {
  color: #e0e0e0;
  font-weight: 600;
}

/* 任务需求区域 */
.requirements {
  grid-column: 1 / 3;
  background: rgba(15, 35, 35, 0.4);
  border-radius: 8rpx;
  padding: 8rpx 12rpx;
  border-left: 4rpx solid #4ab37e;
  font-size: 20rpx;
  color: #c0d0c0;
  margin: 3rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.highlight {
  color: #e2b93b;
  font-weight: 700;
}

/* 订单标签容器 */
.order-tags-container {
  grid-column: 1 / 3;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12rpx;
}

/* 订单标签区域 */
.order-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
  flex: 1;
}

.tag {
  background: rgba(74, 179, 126, 0.2);
  color: #4ab37e;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 18rpx;
  font-weight: 600;
  border: 2rpx solid rgba(74, 179, 126, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 48rpx;
}

/* 发布时间区域 */
.publish-time {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 20rpx;
  color: #8db39c;
  white-space: nowrap;
  flex-shrink: 0;
  position: relative;
}

.time-icon {
  font-size: 20rpx;
  color: #4ab37e;
}

.time-text {
  font-weight: 500;
  font-size: 20rpx;
}

/* 时间加载状态 */
.time-loading {
  color: #999;
  font-style: italic;
}

/* 新订单指示器 */
.new-order-indicator {
  margin-left: 12rpx;
  padding: 4rpx 10rpx;
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  border-radius: 16rpx;
  animation: pulse 2s infinite;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.loading-text {
  font-size: 18rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

@keyframes pulse {
  0% {
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3), 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3), 0 0 0 8rpx rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3), 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

/* 右侧操作区 */
.action-panel {
  width: 200rpx;
  display: flex;
  flex-direction: column;
  border-left: 2rpx solid rgba(74, 179, 126, 0.2);
}

/* 状态显示区 */
.status-display {
  padding: 16rpx;
  text-align: center;
  background: rgba(30, 60, 80, 0.8);
  color: #e2b93b;
  font-weight: 700;
  font-size: 22rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 订单类型信息项 */
.order-type-item {
  justify-content: flex-end;
}

/* 订单类型标签 */
.order-type-tag {
  font-size: 18rpx;
  background: rgba(74, 179, 126, 0.2);
  color: #4ab37e;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  font-weight: 600;
  border: 1rpx solid rgba(74, 179, 126, 0.3);
  display: inline-block;
  text-align: center;
}

.status-tag {
  font-size: 18rpx;
  background: rgba(226, 185, 59, 0.2);
  color: #e2b93b;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  font-weight: 600;
}

/* 操作按钮区 */
.action-buttons {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16rpx 12rpx;
  gap: 12rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  border-radius: 12rpx;
  font-weight: 600;
  font-size: 20rpx;
  min-height: 50rpx;
}

.btn-icon {
  font-size: 20rpx;
}

.btn-modify {
  background: linear-gradient(to bottom, #2c5c44 0%, #1e4530 100%);
  color: #c0e0c0;
  border: 2rpx solid #3a7d58;
}

.btn-cancel {
  background: linear-gradient(to bottom, #5c2c2c 0%, #451e1e 100%);
  color: #e0c0c0;
  border: 2rpx solid #7d3a3a;
}

.btn-detail {
  background: linear-gradient(to bottom, #2c4c5c 0%, #1e3045 100%);
  color: #c0d0e0;
  border: 2rpx solid #3a587d;
}

.btn-grab {
  background: linear-gradient(to bottom, #4ab37e 0%, #2c8c5c 100%);
  color: #ffffff;
  border: 2rpx solid #5cc48a;
  box-shadow: 0 8rpx 24rpx rgba(74, 179, 126, 0.3);
}



/* 交互效果 */
.action-btn.active {
  transform: scale(0.95);
  opacity: 0.9;
}
