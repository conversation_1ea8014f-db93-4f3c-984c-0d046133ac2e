// 获取公告列表云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 简单的内存缓存
let cache = {
  data: null,
  timestamp: 0,
  expiry: 2 * 60 * 1000 // 2分钟缓存
};

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { 
    page = 1, 
    pageSize = 10, 
    type = '', 
    status = 'active',
    isAdmin = false 
  } = event;

  try {
    console.log('获取公告列表请求:', { page, pageSize, type, status, isAdmin });

    // 检查缓存（仅对非管理员的活跃公告请求使用缓存）
    const cacheKey = `${page}_${pageSize}_${type}_${status}_${isAdmin}`;
    if (!isAdmin && status === 'active' && !type && cache.data && cache.data[cacheKey] &&
        (Date.now() - cache.timestamp < cache.expiry)) {
      console.log('📢 [公告列表] 使用缓存数据');
      return {
        success: true,
        data: cache.data[cacheKey]
      };
    }

    // 构建查询条件
    let whereCondition = {};

    // 管理员可以查看所有状态，普通用户只能查看激活状态
    if (!isAdmin) {
      whereCondition.status = 'active';
      // 普通用户只能查看已生效且未过期的公告
      const now = new Date();
      whereCondition.effectiveTime = _.lte(now);
      whereCondition.expireTime = _.gte(now);
    } else if (status) {
      whereCondition.status = status;
    }

    // 按类型筛选
    if (type) {
      whereCondition.type = type;
    }

    console.log('查询条件:', whereCondition);

    // 获取总数
    const countResult = await db.collection('announcements')
      .where(whereCondition)
      .count();

    const total = countResult.total;

    // 获取分页数据
    const skip = (page - 1) * pageSize;
    
    // 排序：置顶 > 优先级 > 发布时间
    const listResult = await db.collection('announcements')
      .where(whereCondition)
      .orderBy('isTop', 'desc')
      .orderBy('priority', 'asc')
      .orderBy('publishTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();

    // 处理数据
    const announcements = listResult.data.map(item => {
      // 计算是否已过期
      const now = new Date();
      const isExpired = new Date(item.expireTime) < now;
      const isEffective = new Date(item.effectiveTime) <= now;

      return {
        ...item,
        isExpired,
        isEffective,
        // 格式化时间显示
        publishTimeFormatted: formatTime(item.publishTime),
        effectiveTimeFormatted: formatTime(item.effectiveTime),
        expireTimeFormatted: formatTime(item.expireTime),
        // 计算剩余有效天数
        remainingDays: Math.ceil((new Date(item.expireTime) - now) / (1000 * 60 * 60 * 24))
      };
    });

    const result = {
      list: announcements,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };

    // 缓存结果（仅对非管理员的活跃公告请求）
    if (!isAdmin && status === 'active' && !type) {
      try {
        if (!cache.data) cache.data = {};
        cache.data[cacheKey] = result;
        cache.timestamp = Date.now();
        console.log('📢 [公告列表] 数据已缓存');
      } catch (cacheError) {
        console.log('📢 [公告列表] 缓存保存失败:', cacheError);
      }
    }

    return {
      success: true,
      data: result
    };

  } catch (error) {
    console.error('获取公告列表失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 时间格式化函数
function formatTime(date) {
  if (!date) return '';
  
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hour = String(d.getHours()).padStart(2, '0');
  const minute = String(d.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}`;
}
