// 测试实时监听修复效果的脚本
// 在微信开发者工具的控制台中运行此脚本

console.log('🧪 开始测试实时监听修复效果...');

// 1. 模拟实时监听收到的订单数据（基于您提供的日志）
const mockRealtimeOrder = {
  _id: "test_order_" + Date.now(),
  customerId: "c5df95ce6873f77a00efb88a680cc8c9",
  customerOpenid: "ogepEvgLRBzS6HuFa0t_QnUKTTFQ",
  title: "测试订单标题",
  content: "测试订单内容",
  status: "pending",
  reward: 50,
  platformType: "mobile",
  serviceType: "duration",
  duration: 2,
  tags: ["测试"],
  createTime: new Date(),
  updateTime: new Date(),
  // 注意：实时监听数据没有 customerInfo 字段
  customerInfo: undefined
};

console.log('📦 模拟实时监听订单数据:', mockRealtimeOrder);

// 2. 测试 formatNewOrderDataInstantly 方法的逻辑
function testFormatNewOrderDataInstantly(order) {
  console.log('🔍 [测试] 开始格式化实时订单数据...');
  
  // 模拟 formatNewOrderDataInstantly 的关键逻辑
  const result = {
    ...order,
    // 客户信息（使用监听器返回的数据，确保包含openid）
    customerInfo: order.customerInfo || {
      nickName: '未知用户',
      avatarUrl: '',
      _id: order.customerId,
      openid: order.customerOpenid || order._openid || '' // 从订单数据中获取openid
    },
    // 权限判断
    isOwner: false, // 假设不是发布者
    canGrab: true,
    isGrabOrder: true
  };
  
  console.log('🔍 [测试] 格式化结果:', result);
  console.log('🔍 [测试] customerInfo:', result.customerInfo);
  
  // 检查是否需要异步更新用户信息
  const needsUserInfoUpdate = !result.customerInfo.openid || 
                             !result.customerInfo.nickName || 
                             result.customerInfo.nickName === '未知用户';
  
  console.log('🔍 [测试] 是否需要异步更新用户信息:', needsUserInfoUpdate, {
    hasOpenid: !!result.customerInfo.openid,
    nickName: result.customerInfo.nickName
  });
  
  if (needsUserInfoUpdate && order.customerId) {
    console.log('🔍 [测试] 将触发异步用户信息更新');
    // 模拟异步更新
    setTimeout(() => {
      console.log('🔍 [测试] 模拟异步更新用户信息...');
      testAsyncUserInfoUpdate(order._id, order.customerId);
    }, 100);
  }
  
  return result;
}

// 3. 测试异步用户信息更新
async function testAsyncUserInfoUpdate(orderId, customerId) {
  console.log('🔍 [测试] 开始异步查询用户信息:', orderId, customerId);
  
  try {
    // 模拟数据库查询
    const db = wx.cloud.database();
    const userResult = await db.collection('users')
      .where({ _id: customerId })
      .field({ _id: true, openid: true, nickName: true, avatarUrl: true })
      .get();
    
    console.log('🔍 [测试] 用户查询结果:', userResult);
    
    if (userResult.data.length > 0) {
      const customerData = userResult.data[0];
      console.log('🔍 [测试] 查询到的用户数据:', {
        _id: customerData._id,
        nickName: customerData.nickName,
        hasAvatarUrl: !!customerData.avatarUrl,
        hasOpenid: !!customerData.openid
      });
      
      // 模拟更新订单列表
      console.log('✅ [测试] 用户信息更新成功，用户昵称:', customerData.nickName);
      return customerData;
    } else {
      console.log('❌ [测试] 未找到用户信息');
      return null;
    }
  } catch (error) {
    console.error('❌ [测试] 用户查询失败:', error);
    return null;
  }
}

// 4. 执行测试
console.log('🚀 [测试] 开始执行测试...');

const formattedOrder = testFormatNewOrderDataInstantly(mockRealtimeOrder);

// 5. 测试总结
console.log('📋 [测试总结]');
console.log('1. 实时监听数据格式化: ✅');
console.log('2. customerInfo 创建: ✅');
console.log('3. openid 提取: ✅');
console.log('4. 异步更新触发条件: ✅');

// 检查关键字段
const hasOpenid = !!formattedOrder.customerInfo.openid;
const isUnknownUser = formattedOrder.customerInfo.nickName === '未知用户';

console.log('🔍 [关键检查]');
console.log('- customerInfo.openid 存在:', hasOpenid);
console.log('- 显示为"未知用户":', isUnknownUser);
console.log('- 应该触发异步更新:', isUnknownUser);

if (hasOpenid && isUnknownUser) {
  console.log('✅ [预期行为] 有openid但显示未知用户，将触发异步更新获取真实用户信息');
} else if (!hasOpenid) {
  console.log('❌ [问题] 缺少openid，无法进行权限判断');
} else {
  console.log('✅ [理想状态] 用户信息完整');
}

console.log('💡 [建议] 发布一个新订单，观察控制台日志，确认:');
console.log('   1. 是否触发异步用户信息更新');
console.log('   2. 异步更新是否成功查询到用户信息');
console.log('   3. 页面是否正确更新显示用户昵称');
