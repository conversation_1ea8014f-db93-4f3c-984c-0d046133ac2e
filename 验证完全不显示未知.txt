// 🔍 验证完全不显示"未知"的测试脚本
console.log('🔍 开始验证完全不显示"未知"...');

// 测试数据：各种异常情况
const testCases = [
  { name: 'null值', value: null },
  { name: 'undefined值', value: undefined },
  { name: '空字符串', value: '' },
  { name: '空对象', value: {} },
  { name: '无效日期字符串', value: 'invalid-date' },
  { name: '无效数字', value: NaN },
  { name: '非时间对象', value: { foo: 'bar' } },
  { name: '正常时间', value: new Date().toISOString() }
];

// 1. 测试首页formatTime方法
console.log('\n📋 测试首页formatTime方法:');
const currentPage = getCurrentPages()[getCurrentPages().length - 1];

if (currentPage.route === 'pages/index/index' && currentPage.formatTime) {
  testCases.forEach(testCase => {
    try {
      const result = currentPage.formatTime(testCase.value);
      const status = (result === '未知' || result === '时间未知') ? '❌ 失败' : '✅ 通过';
      console.log(`  ${status} ${testCase.name}: "${result}"`);
    } catch (error) {
      console.error(`  ❌ 错误 ${testCase.name}:`, error);
    }
  });
} else {
  console.log('  ⚠️ 当前不在首页或formatTime方法不存在');
}

// 2. 测试订单卡片组件formatTime方法
console.log('\n🎴 测试订单卡片组件formatTime方法:');
if (currentPage.route === 'pages/index/index') {
  const orderCardComponents = currentPage.selectAllComponents('.order-card');
  
  if (orderCardComponents.length > 0) {
    const component = orderCardComponents[0];
    
    if (component.formatTime) {
      testCases.forEach(testCase => {
        try {
          const result = component.formatTime(testCase.value);
          const status = (result === '未知' || result === '时间未知') ? '❌ 失败' : '✅ 通过';
          console.log(`  ${status} ${testCase.name}: "${result}"`);
        } catch (error) {
          console.error(`  ❌ 错误 ${testCase.name}:`, error);
        }
      });
    } else {
      console.log('  ⚠️ 订单卡片组件formatTime方法不存在');
    }
  } else {
    console.log('  ⚠️ 没有找到订单卡片组件');
  }
} else {
  console.log('  ⚠️ 当前不在首页');
}

// 3. 检查当前显示的所有订单卡片
console.log('\n🔍 检查当前显示的订单卡片:');
if (currentPage.route === 'pages/index/index') {
  const orderCardComponents = currentPage.selectAllComponents('.order-card');
  console.log(`找到${orderCardComponents.length}个订单卡片组件`);
  
  let hasUnknown = false;
  
  orderCardComponents.forEach((component, index) => {
    const timeDisplay = component.data.formattedCreateTime;
    
    if (timeDisplay === '时间未知' || timeDisplay === '未知') {
      console.error(`❌ 组件${index + 1}: 显示"${timeDisplay}"`);
      hasUnknown = true;
      
      // 显示详细信息
      console.log(`  详细信息:`, {
        orderData: component.data.orderData,
        createTime: component.data.orderData?.createTime,
        createTimeType: typeof component.data.orderData?.createTime,
        isDataReady: component.data.isDataReady
      });
    } else {
      const displayText = timeDisplay || '(空字符串)';
      console.log(`✅ 组件${index + 1}: ${displayText}`);
    }
  });
  
  if (!hasUnknown) {
    console.log('🎉 所有组件都没有显示"未知"！');
  }
} else {
  console.log('⚠️ 当前不在首页');
}

// 4. 模拟异常数据测试
console.log('\n🧪 模拟异常数据测试:');
if (currentPage.route === 'pages/index/index') {
  const orderCardComponents = currentPage.selectAllComponents('.order-card');
  
  if (orderCardComponents.length > 0) {
    const testComponent = orderCardComponents[0];
    
    console.log('测试组件updateFormattedTime方法处理异常数据:');
    
    // 备份原始数据
    const originalData = { ...testComponent.data.orderData };
    
    testCases.forEach(testCase => {
      try {
        // 设置测试数据
        testComponent.setData({
          orderData: {
            ...originalData,
            createTime: testCase.value
          }
        });
        
        // 调用updateFormattedTime
        if (testComponent.updateFormattedTime) {
          testComponent.updateFormattedTime();
          
          const result = testComponent.data.formattedCreateTime;
          const status = (result === '未知' || result === '时间未知') ? '❌ 失败' : '✅ 通过';
          const displayText = result || '(空字符串)';
          console.log(`  ${status} ${testCase.name}: "${displayText}"`);
        }
      } catch (error) {
        console.error(`  ❌ 错误 ${testCase.name}:`, error);
      }
    });
    
    // 恢复原始数据
    testComponent.setData({
      orderData: originalData
    });
    if (testComponent.updateFormattedTime) {
      testComponent.updateFormattedTime();
    }
    
    console.log('✅ 测试完成，已恢复原始数据');
  }
}

// 5. 持续监控
console.log('\n⏱️ 开始10秒持续监控:');
let monitorCount = 0;
const monitor = setInterval(() => {
  monitorCount++;
  
  if (getCurrentPages()[getCurrentPages().length - 1].route === 'pages/index/index') {
    const components = getCurrentPages()[getCurrentPages().length - 1].selectAllComponents('.order-card');
    let foundUnknown = false;
    
    components.forEach((component, index) => {
      const timeDisplay = component.data.formattedCreateTime;
      if (timeDisplay === '时间未知' || timeDisplay === '未知') {
        console.error(`❌ 第${monitorCount}次检查: 组件${index + 1}显示"${timeDisplay}"`);
        foundUnknown = true;
      }
    });
    
    if (!foundUnknown) {
      console.log(`✅ 第${monitorCount}次检查: 所有组件正常`);
    }
  }
  
  if (monitorCount >= 10) {
    clearInterval(monitor);
    console.log('\n🏁 监控结束');
    
    // 最终总结
    console.log('\n📊 测试总结:');
    console.log('✅ formatTime方法已修复，异常情况返回空字符串');
    console.log('✅ updateFormattedTime方法已修复，异常情况设置空字符串');
    console.log('✅ 最终安全检查已添加，确保不显示"未知"');
    console.log('🎯 现在时间异常时将不显示任何内容，而不是"未知"');
  }
}, 1000);

console.log('\n✅ 验证脚本启动完成！');
