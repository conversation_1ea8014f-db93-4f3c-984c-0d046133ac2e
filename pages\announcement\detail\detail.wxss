/* 公告详情页面样式 */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #0f1419;
}

/* 科技感装饰背景 */
.tech-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-gradient);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 100rpx 100rpx;
  animation: dataFlow 20s linear infinite;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: var(--cyber-blue);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
  box-shadow: 0 0 10rpx var(--cyber-blue);
}

.particle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: 1s;
}

.particle:nth-child(3) {
  top: 40%;
  left: 30%;
  animation-delay: 2s;
}

@keyframes dataFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(20rpx, 20rpx); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.scrollarea {
  flex: 1;
  overflow-y: auto;
  height: 100%;
}

.scrollarea.page-with-custom-nav {
  padding-top: calc(var(--nav-bar-height, 94px) + 10rpx);
  box-sizing: border-box;
}

.container {
  padding: 0 24rpx calc(env(safe-area-inset-bottom) + 40rpx) 24rpx;
}

/* 公告卡片样式 */
.announcement-card {
  background: linear-gradient(135deg, rgba(30, 45, 61, 0.9), rgba(21, 32, 43, 0.9));
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid rgba(255, 193, 7, 0.3);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.4);
  position: relative;
  overflow: hidden;
}

.announcement-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, transparent, #ffc107, transparent);
  animation: scanLine 3s ease-in-out infinite;
}

@keyframes scanLine {
  0%, 100% { opacity: 0; transform: translateX(-100%); }
  50% { opacity: 1; transform: translateX(100%); }
}

.announcement-header {
  margin-bottom: 24rpx;
}

.announcement-type {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.announcement-type.urgent {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 77, 79, 0.3);
}

.announcement-type.system {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);
}

.announcement-type.notice {
  background: linear-gradient(135deg, #ffc107, #ff8f00);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 193, 7, 0.3);
}

.type-icon {
  font-size: 20rpx;
}

.type-text {
  font-size: 22rpx;
  line-height: 1;
}

.announcement-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.4;
  margin-bottom: 24rpx;
  text-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.1);
}

.announcement-meta {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 32rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.meta-value {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}

.announcement-content {
  margin-bottom: 24rpx;
}

.content-text {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.8;
  word-break: break-word;
  white-space: pre-wrap;
}

.announcement-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 77, 79, 0.1);
  border: 1rpx solid rgba(255, 77, 79, 0.3);
  border-radius: 12rpx;
  margin-top: 20rpx;
}

.status-icon {
  font-size: 20rpx;
}

.status-text {
  font-size: 24rpx;
  color: #ff4d4f;
  font-weight: 500;
}

/* 相关公告样式 */
.related-announcements {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.title-icon {
  font-size: 24rpx;
  color: var(--primary-color);
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.related-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.related-item:active {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(0.98);
}

.related-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.related-title {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.related-time {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.related-arrow {
  padding: 0 8rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  font-weight: bold;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 24rpx;
  min-height: 400rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 212, 255, 0.2);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 30rpx;
  text-align: center;
}

.retry-btn {
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 12rpx 32rpx;
  font-size: 26rpx;
  font-weight: 600;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 动画效果 */
.fade-in {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
