// 🚨 立即修复"时间未知"显示的紧急脚本 - 设置为空不显示
console.log('🚨 开始立即修复"时间未知"显示，改为不显示任何内容...');

// 1. 获取当前页面
const currentPage = getCurrentPages()[getCurrentPages().length - 1];

if (currentPage.route === 'pages/index/index') {
  // 2. 强制修复所有订单卡片组件
  console.log('🔧 强制修复所有订单卡片组件...');
  
  const orderCardComponents = currentPage.selectAllComponents('.order-card');
  console.log(`找到${orderCardComponents.length}个订单卡片组件`);
  
  let fixedCount = 0;
  
  orderCardComponents.forEach((component, index) => {
    const currentTime = component.data.formattedCreateTime;
    
    if (currentTime === '时间未知' || currentTime === '未知') {
      console.log(`🔧 修复组件${index + 1}: "${currentTime}" → "空字符串(不显示)"`);

      // 强制设置为空字符串，不显示任何内容，并重置重试状态
      component.setData({
        formattedCreateTime: '',
        timeRetryCount: 0,
        isTimeLoading: false
      });
      
      fixedCount++;
    } else {
      console.log(`✅ 组件${index + 1}: "${currentTime}" (无需修复)`);
    }
  });
  
  console.log(`✅ 修复完成！共修复了${fixedCount}个组件`);
  
  // 3. 验证修复结果
  setTimeout(() => {
    console.log('\n🔍 验证修复结果...');
    const components = currentPage.selectAllComponents('.order-card');
    let hasUnknown = false;
    
    components.forEach((component, index) => {
      const timeDisplay = component.data.formattedCreateTime;
      if (timeDisplay === '时间未知' || timeDisplay === '未知') {
        console.error(`❌ 组件${index + 1}: 仍显示"${timeDisplay}"`);
        hasUnknown = true;
      } else {
        console.log(`✅ 组件${index + 1}: "${timeDisplay}"`);
      }
    });
    
    if (!hasUnknown) {
      console.log('🎉 所有组件都已修复！不再显示"时间未知"');
    } else {
      console.error('⚠️ 仍有组件显示"时间未知"，可能需要进一步调试');
    }
  }, 500);
  
  // 4. 设置持续监控，防止问题再次出现
  console.log('\n⏱️ 设置持续监控，防止问题再次出现...');
  
  let monitorCount = 0;
  const continuousMonitor = setInterval(() => {
    monitorCount++;
    
    const components = currentPage.selectAllComponents('.order-card');
    let needsFix = false;
    
    components.forEach((component, index) => {
      const timeDisplay = component.data.formattedCreateTime;
      if (timeDisplay === '时间未知' || timeDisplay === '未知') {
        console.warn(`🔧 监控发现问题: 组件${index + 1}显示"${timeDisplay}"，立即修复为空`);
        component.setData({
          formattedCreateTime: ''
        });
        needsFix = true;
      }
    });
    
    if (needsFix) {
      console.log(`🔧 第${monitorCount}次监控: 发现并修复了问题`);
    } else {
      console.log(`✅ 第${monitorCount}次监控: 一切正常`);
    }
    
    // 监控30秒
    if (monitorCount >= 30) {
      clearInterval(continuousMonitor);
      console.log('🏁 持续监控结束');
    }
  }, 1000);
  
} else {
  console.log('⚠️ 当前不在首页，无法修复订单卡片');
}

// 5. 提供手动修复函数
window.fixTimeUnknown = function() {
  console.log('🔧 执行手动修复...');
  const page = getCurrentPages()[getCurrentPages().length - 1];
  if (page.route === 'pages/index/index') {
    const components = page.selectAllComponents('.order-card');
    components.forEach((component, index) => {
      const currentTime = component.data.formattedCreateTime;
      if (currentTime === '时间未知' || currentTime === '未知') {
        component.setData({
          formattedCreateTime: ''
        });
        console.log(`🔧 强制修复组件${index + 1}: "${currentTime}" → "空(不显示)"`);
      }
    });
    console.log('✅ 手动修复完成！');
  }
};

console.log('\n✅ 紧急修复脚本执行完成！');
console.log('💡 如果问题再次出现，请在控制台执行: fixTimeUnknown()');
console.log('📱 建议刷新页面以应用最新的代码修复');
