// 测试所有可能导致时间显示"未知"的场景
// 在微信开发者工具的控制台中运行此脚本

console.log('🧪 开始测试所有可能的时间显示问题场景...');

// 1. 模拟各种数据处理方法
const mockMethods = {
  // 模拟formatOrderData方法
  formatOrderData(order) {
    console.log('🔍 [formatOrderData] 输入:', order.createTime);
    const result = {
      ...order,
      createTime: order.createTime instanceof Date ? order.createTime.toISOString() : order.createTime,
      updateTime: order.updateTime instanceof Date ? order.updateTime.toISOString() : order.updateTime
    };
    console.log('🔍 [formatOrderData] 输出:', result.createTime);
    return result;
  },

  // 模拟formatNewOrderDataInstantly方法
  formatNewOrderDataInstantly(order) {
    console.log('🔍 [formatNewOrderDataInstantly] 输入:', order.createTime);
    const result = {
      ...order,
      createTime: order.createTime instanceof Date ? order.createTime.toISOString() : order.createTime,
      updateTime: order.updateTime instanceof Date ? order.updateTime.toISOString() : order.updateTime
    };
    console.log('🔍 [formatNewOrderDataInstantly] 输出:', result.createTime);
    return result;
  },

  // 模拟updateOrderInList方法
  updateOrderInList(existingOrder, updatedOrder) {
    console.log('🔍 [updateOrderInList] 现有订单createTime:', existingOrder.createTime);
    console.log('🔍 [updateOrderInList] 更新订单updateTime:', updatedOrder.updateTime);
    const result = {
      ...existingOrder,
      updateTime: updatedOrder.updateTime instanceof Date ? updatedOrder.updateTime.toISOString() : updatedOrder.updateTime
    };
    console.log('🔍 [updateOrderInList] 输出createTime:', result.createTime);
    console.log('🔍 [updateOrderInList] 输出updateTime:', result.updateTime);
    return result;
  },

  // 模拟enrichOrdersDataSilently方法
  enrichOrdersDataSilently(orders) {
    console.log('🔍 [enrichOrdersDataSilently] 输入订单数量:', orders.length);
    const result = orders.map(order => ({
      ...order,
      createTime: order.createTime instanceof Date ? order.createTime.toISOString() : order.createTime,
      updateTime: order.updateTime instanceof Date ? order.updateTime.toISOString() : order.updateTime
    }));
    console.log('🔍 [enrichOrdersDataSilently] 输出第一个订单createTime:', result[0]?.createTime);
    return result;
  },

  // 模拟addNewOrdersToHomePageInstantly方法
  addNewOrdersToHomePageInstantly(order) {
    console.log('🔍 [addNewOrdersToHomePageInstantly] 输入:', order.createTime);
    const result = {
      ...order,
      createTime: order.createTime instanceof Date ? order.createTime.toISOString() : order.createTime,
      updateTime: order.updateTime instanceof Date ? order.updateTime.toISOString() : order.updateTime
    };
    console.log('🔍 [addNewOrdersToHomePageInstantly] 输出:', result.createTime);
    return result;
  }
};

// 2. 模拟setData序列化
function mockSetData(data) {
  console.log('📡 [setData] 序列化前:', data);
  const serialized = JSON.stringify(data);
  const deserialized = JSON.parse(serialized);
  console.log('📡 [setData] 序列化后:', deserialized);
  return deserialized;
}

// 3. 测试场景1：初始加载
function testScenario1_InitialLoad() {
  console.log('\n🎬 [场景1] 初始加载测试:');
  
  const mockOrder = {
    _id: 'test_order_1',
    title: '测试订单',
    createTime: new Date(),
    updateTime: new Date(),
    status: 'pending'
  };
  
  console.log('原始订单:', mockOrder);
  
  // 步骤1: formatOrderData处理
  const formatted = mockMethods.formatOrderData(mockOrder);
  
  // 步骤2: setData序列化
  const afterSetData = mockSetData({ latestOrders: [formatted] });
  
  // 步骤3: 组件接收数据
  const finalCreateTime = afterSetData.latestOrders[0].createTime;
  console.log('组件接收到的createTime:', finalCreateTime, typeof finalCreateTime);
  
  // 步骤4: 时间格式化
  const displayTime = testTimeFormatting(finalCreateTime);
  console.log('最终显示时间:', displayTime);
}

// 4. 测试场景2：实时监听新增
function testScenario2_RealtimeAdd() {
  console.log('\n🎬 [场景2] 实时监听新增测试:');
  
  const mockOrder = {
    _id: 'test_order_2',
    title: '实时新订单',
    createTime: new Date(),
    updateTime: new Date(),
    status: 'pending'
  };
  
  console.log('实时监听接收到的订单:', mockOrder);
  
  // 步骤1: formatNewOrderDataInstantly处理
  const formatted = mockMethods.formatNewOrderDataInstantly(mockOrder);
  
  // 步骤2: setData序列化
  const afterSetData = mockSetData({ latestOrders: [formatted] });
  
  // 步骤3: 组件接收数据
  const finalCreateTime = afterSetData.latestOrders[0].createTime;
  console.log('组件接收到的createTime:', finalCreateTime, typeof finalCreateTime);
  
  // 步骤4: 时间格式化
  const displayTime = testTimeFormatting(finalCreateTime);
  console.log('最终显示时间:', displayTime);
}

// 5. 测试场景3：订单更新
function testScenario3_OrderUpdate() {
  console.log('\n🎬 [场景3] 订单更新测试:');
  
  const existingOrder = {
    _id: 'test_order_3',
    title: '现有订单',
    createTime: '2025-07-31T14:00:00.000Z', // 已经是字符串格式
    updateTime: '2025-07-31T14:00:00.000Z',
    status: 'pending'
  };
  
  const updatedOrder = {
    _id: 'test_order_3',
    title: '更新后的订单',
    createTime: new Date(), // 新的可能是Date对象
    updateTime: new Date(),
    status: 'pending'
  };
  
  console.log('现有订单:', existingOrder);
  console.log('更新数据:', updatedOrder);
  
  // 步骤1: updateOrderInList处理
  const updated = mockMethods.updateOrderInList(existingOrder, updatedOrder);
  
  // 步骤2: setData序列化
  const afterSetData = mockSetData({ latestOrders: [updated] });
  
  // 步骤3: 组件接收数据
  const finalCreateTime = afterSetData.latestOrders[0].createTime;
  console.log('组件接收到的createTime:', finalCreateTime, typeof finalCreateTime);
  
  // 步骤4: 时间格式化
  const displayTime = testTimeFormatting(finalCreateTime);
  console.log('最终显示时间:', displayTime);
}

// 6. 测试场景4：页面切换数据补充
function testScenario4_PageSwitchEnrich() {
  console.log('\n🎬 [场景4] 页面切换数据补充测试:');
  
  const existingOrders = [
    {
      _id: 'test_order_4',
      title: '页面切换订单',
      createTime: '2025-07-31T14:00:00.000Z',
      updateTime: '2025-07-31T14:00:00.000Z',
      status: 'pending'
    }
  ];
  
  console.log('页面切换前的订单:', existingOrders);
  
  // 步骤1: enrichOrdersDataSilently处理
  const enriched = mockMethods.enrichOrdersDataSilently(existingOrders);
  
  // 步骤2: setData序列化
  const afterSetData = mockSetData({ latestOrders: enriched });
  
  // 步骤3: 组件接收数据
  const finalCreateTime = afterSetData.latestOrders[0].createTime;
  console.log('组件接收到的createTime:', finalCreateTime, typeof finalCreateTime);
  
  // 步骤4: 时间格式化
  const displayTime = testTimeFormatting(finalCreateTime);
  console.log('最终显示时间:', displayTime);
}

// 7. 测试场景5：异常数据处理
function testScenario5_AbnormalData() {
  console.log('\n🎬 [场景5] 异常数据处理测试:');
  
  const abnormalCases = [
    { name: '空对象createTime', createTime: {} },
    { name: 'null createTime', createTime: null },
    { name: 'undefined createTime', createTime: undefined },
    { name: '空字符串createTime', createTime: '' },
    { name: '无效字符串createTime', createTime: 'invalid-date' },
    { name: '数字时间戳createTime', createTime: Date.now() },
    { name: '字符串时间戳createTime', createTime: Date.now().toString() }
  ];
  
  abnormalCases.forEach(testCase => {
    console.log(`\n测试 ${testCase.name}:`);
    const mockOrder = {
      _id: 'abnormal_test',
      title: '异常测试订单',
      createTime: testCase.createTime,
      status: 'pending'
    };
    
    try {
      // 通过formatOrderData处理
      const formatted = mockMethods.formatOrderData(mockOrder);
      const afterSetData = mockSetData({ latestOrders: [formatted] });
      const finalCreateTime = afterSetData.latestOrders[0].createTime;
      const displayTime = testTimeFormatting(finalCreateTime);
      
      console.log(`结果: ${displayTime} (createTime: ${JSON.stringify(finalCreateTime)})`);
    } catch (error) {
      console.log(`错误: ${error.message}`);
    }
  });
}

// 8. 时间格式化测试函数
function testTimeFormatting(dateStr) {
  if (!dateStr) {
    return '未知 (空值)';
  }

  // 处理Date对象和字符串两种情况
  const date = dateStr instanceof Date ? dateStr : new Date(dateStr);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return `未知 (无效日期: ${JSON.stringify(dateStr)})`;
  }

  const now = new Date();
  const diff = now - date;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`;
  } else {
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${month}-${day}`;
  }
}

// 9. 运行所有测试
function runAllTests() {
  console.log('🚀 开始运行所有场景测试...\n');
  
  testScenario1_InitialLoad();
  testScenario2_RealtimeAdd();
  testScenario3_OrderUpdate();
  testScenario4_PageSwitchEnrich();
  testScenario5_AbnormalData();
  
  console.log('\n📋 [测试总结]');
  console.log('✅ 所有场景测试完成');
  console.log('💡 如果某个场景显示"未知"，说明该场景存在问题');
  console.log('🔍 请检查对应的数据处理方法是否正确格式化时间字段');
}

// 导出测试函数
window.testAllScenarios = {
  runAllTests,
  testScenario1_InitialLoad,
  testScenario2_RealtimeAdd,
  testScenario3_OrderUpdate,
  testScenario4_PageSwitchEnrich,
  testScenario5_AbnormalData,
  testTimeFormatting
};

console.log('\n🎯 [使用方法]:');
console.log('   testAllScenarios.runAllTests() - 运行所有场景测试');
console.log('   testAllScenarios.testScenario1_InitialLoad() - 测试初始加载');
console.log('   testAllScenarios.testScenario2_RealtimeAdd() - 测试实时新增');

// 自动运行所有测试
runAllTests();
