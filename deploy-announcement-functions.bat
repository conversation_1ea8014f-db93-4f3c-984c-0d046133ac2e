@echo off
echo 🚀 开始部署公告系统云函数...
echo.

echo 📦 部署 initAnnouncementDatabase 云函数...
cd initAnnouncementDatabase
call npm install
echo 正在上传云函数...
echo 请在微信开发者工具中右键点击 initAnnouncementDatabase 文件夹，选择"上传并部署：云端安装依赖"
pause
cd ..

echo.
echo 📦 部署 announcementManager 云函数...
cd announcementManager
call npm install
echo 正在上传云函数...
echo 请在微信开发者工具中右键点击 announcementManager 文件夹，选择"上传并部署：云端安装依赖"
pause
cd ..

echo.
echo 📦 部署 getAnnouncementList 云函数...
cd getAnnouncementList
call npm install
echo 正在上传云函数...
echo 请在微信开发者工具中右键点击 getAnnouncementList 文件夹，选择"上传并部署：云端安装依赖"
pause
cd ..

echo.
echo ✅ 所有公告系统云函数部署完成！
echo 💡 现在您可以运行测试脚本来创建测试公告了
pause
