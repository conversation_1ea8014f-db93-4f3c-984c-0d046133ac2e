// index.js
const API = require('../../utils/api.js');
const { ORDER_STATUS_NAMES } = require('../../utils/constants.js');
const cloudStorage = require('../../utils/cloudStorage.js');
const watcherStateGuard = require('../../utils/watcherStateGuard.js');
const errorHandler = require('../../utils/errorHandler.js');
const realtimeManager = require('../../utils/realtimeManager.js');
const userActivityTracker = require('../../utils/userActivityTracker.js');
// 处理ES6 export default的导入
const cloudStorageInstance = cloudStorage.default || cloudStorage;

const app = getApp();

Page({
  data: {
    banners: [], // 将从云存储动态加载
    announcements: [], // 公告列表

    latestOrders: [],
    loading: false,
    navLoading: false, // 导航栏加载状态
    floatingIcon: '',
    iconLoading: true, // 图标加载状态
    backgroundImage: '', // 背景图片URL
    hasBackgroundImage: false, // 是否有自定义背景图片
    themeLoaded: true, // 主题加载状态
    // 实时监听相关
    orderWatcher: null,
    isWatcherActive: false,
    // 重连相关
    reconnectAttempts: 0,
    maxReconnectAttempts: 3, // 首页重连次数较少

    // 通知相关
    notification: {
      show: false,
      type: 'message',
      title: '',
      content: '',
      avatar: '',
      duration: 7000 // 增加到7秒，给用户更多时间查看
    },

    // 重连相关
    reconnectTimer: null,
    pollingTimer: null, // 轮询定时器
    autoRefreshTimer: null, // 抢单大厅自动刷新定时器
    // 每日密码数据 - 初始为空，等待云函数获取
    dailyPassword: {
      date: '',
      locations: []
    },
    // 密码加载状态
    passwordLoading: true,
    // 矩阵效果字符 - 4位数字用于密码显示
    matrixChars: ['0', '1', '2', '3']

  },

  async onLoad() {
    console.log('=== 首页加载 ===');

    // 保存原始方法
    this.originalShowLoading = app.utils.showLoading;
    this.originalWxShowLoading = wx.showLoading;

    // 临时禁用所有loading显示
    app.utils.showLoading = () => {
      console.log('🚫 [首页] 阻止app.utils.showLoading调用');
    };

    wx.showLoading = () => {
      console.log('🚫 [首页] 阻止wx.showLoading调用');
    };

    // 立即隐藏任何可能的系统加载提示 - 多次调用确保生效
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);
    setTimeout(() => wx.hideLoading(), 200);

    // 确保导航栏loading状态为false
    this.setData({ navLoading: false });

    this.checkLoginStatus();

    // 调试：输出当前状态
    console.log('=== 首页加载调试信息 ===');
    console.log('app.globalData.userInfo:', app.globalData.userInfo);
    console.log('app.globalData.isLogin:', app.globalData.isLogin);

    console.log('当前页面 loading:', this.data.loading);
    console.log('当前页面 latestOrders:', this.data.latestOrders);

    // 并行加载资源
    await Promise.all([
      this.loadBanners(),
      this.loadAnnouncements(),
      this.initFloatingIcon(),
      this.setBackgroundImage(),
      this.ensureUserInfoLoaded()
    ]);

    await this.loadData();

    // 在数据加载完成后启动实时监听
    setTimeout(() => {
      this.startOrderWatcher();
    }, 500);

    // 启动抢单大厅自动刷新机制
    this.startAutoRefresh();

    // 页面加载完成后，恢复原始的showLoading方法（延迟恢复，确保初始加载完成）
    setTimeout(() => {
      app.utils.showLoading = this.originalShowLoading;
      wx.showLoading = this.originalWxShowLoading;
      console.log('✅ [首页] 已恢复原始showLoading方法');
    }, 1000);

    // 预热抢单云函数，减少首次抢单的冷启动延迟
    setTimeout(() => {
      API.warmupGrabOrder();
    }, 2000);

    console.log('🎮 科技感主题已启用');

    // 设置通知事件监听
    this.setupNotificationListeners();

    // 获取最新每日密码（无感加载）
    this.fetchDailyPasswordSilently();
  },

  // 无感获取每日密码（页面加载时调用）
  async fetchDailyPasswordSilently() {
    try {
      // 启动矩阵效果
      this.startMatrixEffect();

      // 模拟破译过程的延迟，增加科技感
      await new Promise(resolve => setTimeout(resolve, 1500));

      const result = await wx.cloud.callFunction({
        name: 'getDailyPassword'
      });

      if (result.result && result.result.data) {
        const passwordData = result.result.data;

        // 模拟破译完成的延迟
        await new Promise(resolve => setTimeout(resolve, 800));

        // 设置密码数据
        this.setData({
          dailyPassword: {
            date: passwordData.date,
            locations: passwordData.locations
          },
          passwordLoading: false
        });
      } else {
        throw new Error('云函数返回数据格式错误');
      }

    } catch (error) {
      console.error('🔐 [密码破译] 破译失败:', error);

      // 模拟破译失败的延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 显示破译失败状态
      const today = new Date();
      const month = (today.getMonth() + 1).toString().padStart(2, '0');
      const day = today.getDate().toString().padStart(2, '0');
      const dateStr = `${month}月${day}日`;

      this.setData({
        dailyPassword: {
          date: dateStr,
          locations: [
            { name: '零号大坝', code: '----' },
            { name: '长弓溪谷', code: '----' },
            { name: '巴克什', code: '----' },
            { name: '航天基地', code: '----' },
            { name: '潮汐监狱', code: '----' }
          ]
        },
        passwordLoading: false
      });
    }
  },

  // 启动矩阵效果
  startMatrixEffect() {
    const chars = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const matrixInterval = setInterval(() => {
      if (!this.data.passwordLoading) {
        clearInterval(matrixInterval);
        return;
      }

      // 生成4位随机数字用于密码破译显示
      const newChars = Array.from({ length: 4 }, () =>
        chars[Math.floor(Math.random() * chars.length)]
      );

      this.setData({
        matrixChars: newChars
      });
    }, 150);
  },

  // 手动刷新每日密码
  async fetchDailyPassword() {
    try {
      // 显示加载状态
      this.setData({
        passwordLoading: true
      });

      // 启动矩阵效果
      this.startMatrixEffect();

      // 模拟破译过程
      await new Promise(resolve => setTimeout(resolve, 1200));

      const result = await wx.cloud.callFunction({
        name: 'getDailyPassword'
      });

      if (result.result && result.result.data) {
        const passwordData = result.result.data;

        // 模拟破译完成延迟
        await new Promise(resolve => setTimeout(resolve, 600));

        this.updateDailyPassword(passwordData);
        this.setData({ passwordLoading: false });
      } else {
        throw new Error('云函数返回数据格式错误');
      }

    } catch (error) {
      console.error('🔐 [手动刷新] 破译过程失败:', error);

      // 模拟失败延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      this.setData({ passwordLoading: false });
      // 静默处理系统故障，不显示提示
    }
  },

  // 更新每日密码（可以通过云函数或其他方式调用）
  updateDailyPassword(newPasswordData) {
    if (newPasswordData && newPasswordData.locations) {
      this.setData({
        dailyPassword: {
          date: newPasswordData.date || this.data.dailyPassword.date,
          locations: newPasswordData.locations
        }
      });
      console.log('🔐 [每日密码] 密码已更新:', newPasswordData);
    }
  },

  // 复制单个密码
  copyPassword(e) {
    const { location, code } = e.currentTarget.dataset;

    // 检查是否为破译失败状态
    if (code === '----') {
      wx.showToast({
        title: `${location}密码破译失败`,
        icon: 'error',
        duration: 1500
      });
      return;
    }

    const copyText = code; // 只复制密码数字

    wx.setClipboardData({
      data: copyText,
      success: () => {
        wx.showToast({
          title: `已复制${location}密码`,
          icon: 'success',
          duration: 1500
        });
        console.log('🔐 [密码复制] 单个密码复制成功:', copyText);
      },
      fail: (err) => {
        console.error('🔐 [密码复制] 单个密码复制失败:', err);
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        });
      }
    });
  },

  // 复制全部密码
  copyAllPasswords() {
    const { date, locations } = this.data.dailyPassword;

    // 检查是否有破译失败的密码
    const failedCount = locations.filter(location => location.code === '----').length;
    if (failedCount > 0) {
      wx.showToast({
        title: `有${failedCount}个密码破译失败`,
        icon: 'error',
        duration: 2000
      });
      return;
    }

    let copyText = `${date}密码已更新\n`;

    locations.forEach(location => {
      copyText += `${location.name}：${location.code}\n`;
    });

    // 去除最后一个换行符
    copyText = copyText.trim();

    wx.setClipboardData({
      data: copyText,
      success: () => {
        wx.showToast({
          title: '已复制全部密码',
          icon: 'success',
          duration: 2000
        });
        console.log('🔐 [密码复制] 全部密码复制成功:', copyText);
      },
      fail: (err) => {
        console.error('🔐 [密码复制] 全部密码复制失败:', err);
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        });
      }
    });
  },

  onShow() {
    console.log('=== 首页显示 ===');

    // 记录用户活跃
    userActivityTracker.recordActivity();

    // 获取app实例
    const app = getApp();

    // 临时禁用所有loading显示
    if (this.originalShowLoading) {
      app.utils.showLoading = () => {
        console.log('🚫 [首页显示] 阻止app.utils.showLoading调用');
      };
    }

    if (this.originalWxShowLoading) {
      wx.showLoading = () => {
        console.log('🚫 [首页显示] 阻止wx.showLoading调用');
      };
    }

    // 确保隐藏任何可能的系统加载提示 - 多次调用确保生效
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);

    // 确保导航栏loading状态为false
    this.setData({ navLoading: false });

    // 检查是否需要刷新数据
    const needRefresh = app.globalData.needRefreshHomePage;
    if (needRefresh) {
      console.log('🔄 [全局刷新] 检测到需要刷新首页');
      app.globalData.needRefreshHomePage = false;

      // 只有在需要刷新时才重置数据并重新加载
      this.setData({
        latestOrders: [],
        loading: false
      });
      console.log('🔄 [页面切换] 触发数据重新加载');
      this.loadData();
    } else if (this.data.latestOrders.length === 0) {
      // 如果没有数据且不是刷新场景，进行初始加载
      console.log('🔄 [初始加载] 页面无数据，开始加载');
      // 确保用户信息已加载再加载数据
      this.ensureUserInfoLoaded().then(() => {
        this.loadData();
      });
    } else {
      console.log('✅ [页面显示] 已有数据，无需重新加载');
      // 强制重置加载状态，防止卡住
      this.setData({ loading: false });

      // 🔧 暂时禁用时间字段验证，避免破坏有效的时间数据
      // this.validateAndFixOrderTimeFields();
      console.log('🔧 [页面显示] 跳过时间字段验证，保持原始数据不变');
    }

    // 检查是否需要重新启动实时监听
    if (!this.data.isWatcherActive) {
      console.log('🔄 [首页显示] 检测到监听器未活跃，重新启动监听器');
      setTimeout(() => {
        this.startOrderWatcher();
      }, 500);
    }

    // 确保自动刷新机制正在运行
    this.startAutoRefresh();

    // 更新 tabBar 选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected();
    }

    // 页面显示完成后，恢复原始的showLoading方法
    setTimeout(() => {
      if (this.originalShowLoading) {
        app.utils.showLoading = this.originalShowLoading;
      }
      if (this.originalWxShowLoading) {
        wx.showLoading = this.originalWxShowLoading;
      }
      console.log('✅ [首页显示] 已恢复原始showLoading方法');
    }, 500);
  },

  onHide() {
    console.log('=== 首页隐藏 ===');
    // 确保隐藏任何可能的系统加载提示
    wx.hideLoading();
    // 页面隐藏时停止实时监听和自动刷新
    this.stopOrderWatcher();
    this.stopAutoRefresh();
  },

  onUnload() {
    console.log('=== 首页卸载 ===');
    // 确保隐藏任何可能的系统加载提示
    wx.hideLoading();

    // 恢复原始的showLoading方法
    if (this.originalShowLoading) {
      app.utils.showLoading = this.originalShowLoading;
    }
    if (this.originalWxShowLoading) {
      wx.showLoading = this.originalWxShowLoading;
    }

    // 页面卸载时停止实时监听和自动刷新
    this.stopOrderWatcher();
    this.stopAutoRefresh();
  },



  onPullDownRefresh() {
    // 强制隐藏任何可能的系统加载框
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);

    this.loadData().then(() => {
      // 强制隐藏任何可能的系统加载框
      wx.hideLoading();
      setTimeout(() => wx.hideLoading(), 10);

      wx.stopPullDownRefresh();
    });
  },

  // 加载轮播图
  async loadBanners() {
    try {
      // 确保隐藏系统loading
      wx.hideLoading();

      // 云存储文件ID列表（使用正确的环境ID）
      const bannerFileIds = [
        'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/banners/banner1.jpg',
        'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/banners/banner2.jpg',
        'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/banners/banner3.jpg'
      ];

      // 获取临时URL
      const result = await wx.cloud.getTempFileURL({
        fileList: bannerFileIds
      });

      // 立即隐藏可能出现的系统loading
      wx.hideLoading();

      console.log('📁 轮播图云存储响应:', result);

      if (result.fileList && result.fileList.length > 0) {
        const banners = result.fileList
          .filter(file => file.status === 0) // 只保留成功的文件
          .map((file, index) => ({
            image: file.tempFileURL,
            title: '' // 不显示标题
          }));

        if (banners.length > 0) {
          this.setData({ banners });
          console.log('✅ 轮播图加载成功:', banners);
          console.log('📊 当前页面 banners 数据:', this.data.banners);
          return;
        }
      }

      // 如果没有成功加载任何图片，使用备选方案
      throw new Error('没有成功加载任何轮播图');

    } catch (error) {
      console.error('❌ 轮播图加载失败:', error);
      // 使用简单的占位图作为最后备选
      this.setData({
        banners: [
          {
            image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzc1IiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veS4rS4uLjwvdGV4dD48L3N2Zz4=',
            title: '图片加载中...'
          }
        ]
      });
    }
  },

  // 初始化浮动按钮图标
  async initFloatingIcon() {
    try {
      // 确保隐藏系统loading
      wx.hideLoading();

      // 使用临时URL方式获取云存储图标
      const cloudFileId = 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/icons/your-icon.png';

      const result = await wx.cloud.getTempFileURL({
        fileList: [cloudFileId]
      });

      // 立即隐藏可能出现的系统loading
      wx.hideLoading();

      if (result.fileList && result.fileList.length > 0 && result.fileList[0].status === 0) {
        const floatingIcon = result.fileList[0].tempFileURL;
        this.setData({
          floatingIcon,
          iconLoading: false
        });
      } else {
        throw new Error('云存储图标获取失败');
      }

    } catch (error) {
      console.error('❌ [首页] 浮动按钮图标加载失败:', error);
      // 使用文字图标作为备用
      this.setData({
        floatingIcon: '',
        iconLoading: false
      });
      console.log('🔄 [首页] 使用默认样式');
    }
  },



  // 检查登录状态
  checkLoginStatus() {
    if (!app.globalData.isLogin) {
      wx.navigateTo({
        url: '/pages/common/login/login'
      });
    }
  },

  // 加载页面数据
  async loadData() {
    // 如果正在加载，等待一下再重试
    if (this.data.loading) {
      return;
    }

    // 加载抢单大厅订单数据
    await this.loadLatestOrders();
  },



  // 清理文本内容，去除多余空格和换行符
  cleanTextContent(text) {
    if (!text) return '';
    // 将换行符替换为空格，然后去除多余的空格
    return text.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
  },

  // 格式化服务时长
  formatDuration(duration) {
    if (!duration) return '';
    if (typeof duration === 'string') return duration;
    if (typeof duration === 'number') {
      if (duration >= 60) {
        const hours = Math.floor(duration / 60);
        const minutes = duration % 60;
        return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
      } else {
        return `${duration}分钟`;
      }
    }
    return '';
  },

  // 加载抢单大厅订单
  async loadLatestOrders() {
    // 防止重复加载
    if (this.data.loading) {
      console.log('📋 [首页] 正在加载中，跳过重复请求');
      return;
    }

    // 直接进行网络请求加载数据

    console.log('开始加载抢单大厅订单...');

    // 强制隐藏任何可能的系统加载提示，多次调用确保生效
    wx.hideLoading();
    setTimeout(() => wx.hideLoading(), 10);
    setTimeout(() => wx.hideLoading(), 50);
    setTimeout(() => wx.hideLoading(), 100);
    setTimeout(() => wx.hideLoading(), 200);

    // 等待一小段时间确保系统loading被完全隐藏，然后再设置自定义loading
    await new Promise(resolve => setTimeout(resolve, 50));

    // 再次确保系统loading被隐藏
    wx.hideLoading();

    // 只有在没有数据时才显示loading状态，避免白色背景闪烁
    const shouldShowLoading = this.data.latestOrders.length === 0;
    this.setData({ loading: shouldShowLoading });

    try {
      let orders = [];

      // 调用抢单大厅专用API，禁用系统加载提示
      console.log('调用 API.getGrabOrderList...');
      const result = await API.getGrabOrderList({
        page: 1,
        pageSize: 5
      }, { showLoading: false }); // 禁用系统loading提示
      if (result.success) {
        if (result.data.list.length === 0) {
          orders = [];
        } else {
          orders = result.data.list.map(order => {
            return this.formatOrderData(order);
          });
        }
      } else {
        orders = [];
      }

      // 🔧 修复时序问题：确保初始数据就是完整格式化的
      console.log('🕐 [首页] 设置订单数据前，验证时间字段格式:', {
        订单数量: orders.length,
        第一个订单时间: orders[0]?.createTime,
        时间类型: typeof orders[0]?.createTime
      });

      this.setData({
        latestOrders: orders
      });

      // 在页面更新完成后，静默进行后台数据补充（不影响用户界面）
      // 🔧 延迟增加到500ms，确保组件完全初始化后再进行二次更新
      setTimeout(() => {
        this.enrichOrdersDataSilently(orders);
      }, 500);

    } catch (error) {
      console.error('加载抢单大厅订单失败:', error);
      this.setData({
        latestOrders: []
      });
    } finally {
      // 确保隐藏所有loading状态 - 多次调用确保生效
      wx.hideLoading();
      setTimeout(() => wx.hideLoading(), 10);
      setTimeout(() => wx.hideLoading(), 50);
      setTimeout(() => wx.hideLoading(), 100);
      setTimeout(() => wx.hideLoading(), 200);

      this.setData({ loading: false });

      // 延迟再次隐藏，确保系统loading完全消失
      setTimeout(() => wx.hideLoading(), 300);
      setTimeout(() => wx.hideLoading(), 500);
    }
  },









  // 格式化订单数据
  formatOrderData(order) {
    console.log('🔍 [首页初始格式化] 原始订单数据:', order);
    console.log('🔍 [首页初始格式化] 原始platformType:', order.platformType);
    console.log('🔍 [首页初始格式化] 原始customerInfo:', order.customerInfo);

    // 尝试从本地存储获取修改后的数据
    let modifiedData = {};
    try {
      const storedData = wx.getStorageSync(`create_order_${order._id}`);
      if (storedData) {
        modifiedData = storedData;
        console.log('🔍 [首页初始格式化] 本地存储数据:', modifiedData);
      }
    } catch (error) {
      console.warn('读取本地存储订单数据失败:', error);
    }

    // 合并修改后的数据
    const finalOrder = {
      ...order,
      title: modifiedData.title || order.title,
      content: modifiedData.content || order.content,
      reward: modifiedData.reward || order.reward || order.totalAmount,
      // 平台类型
      platformType: modifiedData.platformType || order.platformType || 'pc',
      // 服务类型和时间信息
      serviceType: modifiedData.serviceType || order.serviceType,
      duration: modifiedData.duration || order.duration,
      rounds: modifiedData.rounds || order.rounds,
      tags: modifiedData.tags && modifiedData.tags.length > 0 ? modifiedData.tags : order.tags,
      orderType: modifiedData.orderType || order.orderType,
      scheduledDate: modifiedData.scheduledDate || order.scheduledDate,
      scheduledTime: modifiedData.scheduledTime || order.scheduledTime
    };

    const result = {
      ...finalOrder,
      // 清理文本内容
      content: this.cleanTextContent(finalOrder.content),
      title: this.cleanTextContent(finalOrder.title),
      statusName: ORDER_STATUS_NAMES[finalOrder.status] || finalOrder.status,
      // 格式化时间
      createTimeText: this.formatTime(finalOrder.createTime),
      // 🔧 保持原始时间字段，只对Date对象进行安全转换
      createTime: finalOrder.createTime instanceof Date ? finalOrder.createTime.toISOString() : finalOrder.createTime,
      updateTime: finalOrder.updateTime instanceof Date ? finalOrder.updateTime.toISOString() : finalOrder.updateTime,
      // 确保游戏信息显示正确，优先使用新的title字段
      gameInfo: finalOrder.gameInfo || {
        gameName: this.cleanTextContent(finalOrder.title) || '高阶技术指导',
        gameMode: finalOrder.gameMode || null // 不设置默认游戏模式
      },
      // 确保价格信息显示正确，支持新的reward字段
      pricing: finalOrder.pricing || {
        totalAmount: finalOrder.reward || finalOrder.totalAmount || 0
      },
      totalAmount: finalOrder.reward || finalOrder.pricing?.totalAmount || finalOrder.totalAmount || 0,
      // 确保客户信息存在，保留完整的用户信息
      customerInfo: finalOrder.customerInfo || {
        _id: finalOrder.customerId,
        nickName: '未知用户',
        avatarUrl: '',
        openid: finalOrder.customerOpenid || ''
      },
      // 标签处理
      displayTags: (finalOrder.tags || []).slice(0, 3), // 最多显示3个标签
      // 判断是否为订单发布者
      isOwner: this.isOrderOwner(finalOrder)
    };

    console.log('🔍 [首页初始格式化] 最终platformType:', result.platformType);
    console.log('🔍 [首页初始格式化] 最终customerInfo:', result.customerInfo);
    console.log('🔍 [首页初始格式化] isOwner判断结果:', result.isOwner);
    return result;
  },

  // 确保时间字段为字符串格式（避免setData序列化问题）
  ensureTimeStringFormat(timeValue) {
    // 🔧 修复：对于空值或undefined，返回null
    if (timeValue === null || timeValue === undefined) {
      return null;
    }

    // 🔧 修复：对于空字符串，返回null
    if (timeValue === '') {
      return null;
    }

    // 如果是Date对象，转换为ISO字符串
    if (timeValue instanceof Date) {
      // 检查Date对象是否有效
      if (isNaN(timeValue.getTime())) {
        console.warn('🕐 [首页] 无效的Date对象:', timeValue);
        return null;
      }
      return timeValue.toISOString();
    }

    // 如果是字符串，验证并返回
    if (typeof timeValue === 'string') {
      // 🔧 修复：验证字符串是否是有效的时间格式
      const testDate = new Date(timeValue);
      if (isNaN(testDate.getTime())) {
        console.warn('🕐 [首页] 无效的时间字符串:', timeValue);
        return null;
      }
      return timeValue; // 返回原始有效字符串
    }

    // 如果是数字（时间戳），转换为ISO字符串
    if (typeof timeValue === 'number') {
      const date = new Date(timeValue);
      if (isNaN(date.getTime())) {
        console.warn('🕐 [首页] 无效的时间戳:', timeValue);
        return null;
      }
      return date.toISOString();
    }

    // 如果是空对象（setData序列化Date对象的结果），返回null
    if (typeof timeValue === 'object' && Object.keys(timeValue).length === 0) {
      console.warn('🕐 [首页] 检测到空对象时间字段，可能是setData序列化问题:', timeValue);
      return null;
    }

    // 其他情况，尝试转换
    try {
      const date = new Date(timeValue);
      if (isNaN(date.getTime())) {
        console.warn('🕐 [首页] 无法转换的时间值:', timeValue);
        return null;
      }
      return date.toISOString();
    } catch (error) {
      console.warn('🕐 [首页] 时间格式转换失败:', timeValue, error);
      return null;
    }
  },


  // 验证并修复订单时间字段
  validateAndFixOrderTimeFields() {
    const orders = this.data.latestOrders || [];
    if (orders.length === 0) {
      return;
    }

    let needsUpdate = false;
    const fixedOrders = orders.map(order => {
      const originalCreateTime = order.createTime;
      const fixedCreateTime = this.ensureTimeStringFormat(originalCreateTime);

      // 如果时间字段被修复了，标记需要更新
      if (originalCreateTime !== fixedCreateTime) {
        console.log('🔧 [首页] 修复订单时间字段:', {
          orderId: order._id,
          original: originalCreateTime,
          fixed: fixedCreateTime
        });
        needsUpdate = true;
      }

      return {
        ...order,
        createTime: fixedCreateTime,
        updateTime: this.ensureTimeStringFormat(order.updateTime)
      };
    });

    // 如果有字段被修复，更新页面数据
    if (needsUpdate) {
      console.log('🔧 [首页] 更新修复后的订单数据');
      this.setData({
        latestOrders: fixedOrders
      });
    }
  },
  // 导航到创建订单（空状态按钮）
  navigateToCreate() {
    wx.navigateTo({
      url: '/order-package/pages/create/create'
    });
  },





  // 导航到抢单大厅
  navigateToGrabHall() {
    console.log('🔄 navigateToGrabHall 被调用');
    wx.navigateTo({
      url: '/pages/order/list/list?mode=grab',
      success: function(res) {
        console.log('✅ 导航到抢单大厅成功', res);
      },
      fail: function(err) {
        console.error('❌ 导航到抢单大厅失败', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },



  // 查看抢单详情
  navigateToOrderDetail(e) {
    const orderId = e.detail.orderId || e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/order-package/pages/detail/detail?id=${orderId}`
    });
  },

  // 抢单功能
  async grabOrder(e) {
    console.log('🎯 抢单功能被调用:', e);
    const orderId = e.detail.orderId || e.currentTarget.dataset.id;
    console.log('📋 订单ID:', orderId);

    if (!orderId) {
      console.error('❌ 订单ID为空');
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'error',
        duration: 2000
      });
      return;
    }

    // 防重复点击检查
    if (this.data.isGrabbing) {
      console.log('🚫 [防重复] 正在抢单中，忽略重复点击');
      return;
    }

    try {
      // 设置抢单状态
      this.setData({ isGrabbing: true });

      // 显示加载提示
      wx.showLoading({
        title: '抢单中...',
        mask: true
      });

      console.log('🔄 开始调用抢单API...');
      // 调用抢单API
      const result = await API.grabOrder(orderId);
      console.log('📊 抢单API返回结果:', result);

      // 处理抢单结果
      if (result.success) {
        console.log('✅ 抢单成功');

        wx.hideLoading();
        this.setData({ isGrabbing: false });

        wx.showModal({
          title: '抢单成功！',
          content: '恭喜您成功抢到订单！是否查看我的订单？',
          confirmText: '查看订单',
          cancelText: '继续抢单',
          success: (res) => {
            if (res.confirm) {
              console.log('🔗 跳转到订单详情页面，订单ID:', orderId);
              wx.navigateTo({
                url: `/order-package/pages/detail/detail?id=${orderId}`
              });
            } else {
              this.loadLatestOrders();
            }
          }
        });
      } else {
        // 特殊情况：如果是ORDER_ALREADY_TAKEN且订单已从列表移除，可能是当前用户抢到了
        if (result.errorCode === 'ORDER_ALREADY_TAKEN') {
          const orderStillInList = this.data.latestOrders.some(order => order._id === orderId);

          if (!orderStillInList) {
            console.log('🔍 [快速检查] 订单已移除，可能是当前用户抢到');

            // 使用更快的检查方式：直接查询订单状态
            try {
              // 使用简化的查询，只获取必要字段
              const checkResult = await wx.cloud.database().collection('orders')
                .doc(orderId)
                .field({ accepterId: true, status: true })
                .get();

              if (checkResult.data &&
                  checkResult.data.accepterId === app.globalData.userInfo._id) {
                console.log('✅ [快速检查] 确认是当前用户抢到，显示成功');

                wx.hideLoading();
                this.setData({ isGrabbing: false });

                wx.showModal({
                  title: '抢单成功！',
                  content: '恭喜您成功抢到订单！是否查看我的订单？',
                  confirmText: '查看订单',
                  cancelText: '继续抢单',
                  success: (res) => {
                    if (res.confirm) {
                      wx.navigateTo({
                        url: `/order-package/pages/detail/detail?id=${orderId}`
                      });
                    } else {
                      this.loadLatestOrders();
                    }
                  }
                });
                return;
              }
            } catch (checkError) {
              console.log('🔍 [快速检查] 检查失败，继续显示错误');
            }
          }
        }

        // 立即隐藏loading并处理错误
        wx.hideLoading();

        console.log('❌ [抢单失败] 确认失败，显示错误信息');

        // 确实是抢单失败，处理错误信息
        let errorMessage = result.error || result.message || '抢单失败';
        let shouldRefreshList = false;

        // 优先使用错误代码进行判断
        if (result.errorCode) {
          switch (result.errorCode) {
            case 'ORDER_ALREADY_TAKEN':
            case 'CONCURRENT_CONFLICT':
            case 'MAX_RETRIES_EXCEEDED':
            case 'CONCURRENT_UPDATE_FAILED':
            case 'VERIFICATION_FAILED':
              errorMessage = '此订单已被其他用户抢走';
              shouldRefreshList = true;
              break;
            case 'ORDER_CANCELLED':
              errorMessage = '此订单已被取消';
              shouldRefreshList = true;
              break;
            case 'ORDER_COMPLETED':
              errorMessage = '此订单已完成';
              shouldRefreshList = true;
              break;
            case 'ORDER_NOT_FOUND':
              errorMessage = '订单不存在';
              shouldRefreshList = true;
              break;
            case 'INVALID_STATUS':
              errorMessage = '订单状态已变更，无法抢单';
              shouldRefreshList = true;
              break;
            case 'OWN_ORDER':
              errorMessage = '不能抢自己发布的订单';
              break;
            case 'USER_NOT_FOUND':
              errorMessage = '用户信息异常，请重新登录';
              break;
            default:
              // 使用原始错误消息
              break;
          }
        } else {
          // 兼容旧版本：根据错误信息进行判断
          if (errorMessage.includes('不存在') ||
              errorMessage.includes('已被') ||
              errorMessage.includes('已取消') ||
              errorMessage.includes('订单状态不允许接单') ||
              errorMessage.includes('状态不允许') ||
              errorMessage.includes('订单不可用')) {
            errorMessage = '此订单已被抢或已取消';
            shouldRefreshList = true;
          }
        }

        console.error('❌ [首页抢单失败] 错误处理:', {
          originalError: result.error,
          errorCode: result.errorCode,
          finalMessage: errorMessage,
          shouldRefresh: shouldRefreshList
        });

        // 使用统一错误处理
        errorHandler.handle(new Error(errorMessage), '首页抢单失败');

        // 如果是订单状态相关的错误，刷新列表移除无效订单
        if (shouldRefreshList) {
          setTimeout(() => {
            console.log('🔄 [首页自动刷新] 由于订单状态错误，刷新订单列表');
            this.loadLatestOrders();
          }, 1000);
        }
      }
    } catch (error) {
      wx.hideLoading();
      // 使用统一错误处理
      errorHandler.handle(error, '首页抢单异常');
    } finally {
      // 重置抢单状态
      this.setData({ isGrabbing: false });
    }
  },

  // 编辑订单
  editOrder(e) {
    const orderId = e.detail.orderId;
    const order = this.data.latestOrders.find(item => item._id === orderId);

    if (order) {
      // 构建编辑URL，传递完整的订单数据
      const params = {
        mode: 'edit',
        orderId: orderId,
        title: encodeURIComponent(order.title || ''),
        content: encodeURIComponent(order.content || ''),
        reward: order.reward || order.totalAmount || '',
        platformType: order.platformType || 'pc',
        serviceType: order.serviceType || 'duration',
        duration: order.duration || '2',
        rounds: order.rounds || '5',
        orderType: order.orderType || 'immediate',
        scheduledDate: order.scheduledDate || '',
        scheduledTime: order.scheduledTime || ''
      };

      console.log('🔍 [编辑订单] 传递的参数:', params);

      const queryString = Object.keys(params)
        .map(key => `${key}=${params[key]}`)
        .join('&');

      wx.navigateTo({
        url: `/order-package/pages/create/create?${queryString}`
      });
    } else {
      // 如果找不到订单数据，使用简单模式
      wx.navigateTo({
        url: `/order-package/pages/create/create?mode=edit&orderId=${orderId}`
      });
    }
  },

  // 通用取消原因选择函数
  showCancelReasonSelector(callback) {
    wx.showActionSheet({
      itemList: ['临时有事', '价格问题', '服务质量问题', '沟通问题', '其他问题'],
      success: (res) => {
        const reasons = ['临时有事', '价格问题', '服务质量问题', '沟通问题', '其他问题'];
        const selectedReason = reasons[res.tapIndex];

        if (selectedReason === '其他问题') {
          // 显示输入框让用户输入具体原因
          wx.showModal({
            title: '请输入取消原因',
            content: '请详细说明取消原因：',
            editable: true,
            placeholderText: '请输入具体的取消原因...',
            success: (inputRes) => {
              if (inputRes.confirm) {
                const customReason = inputRes.content.trim();
                if (customReason) {
                  callback(customReason);
                } else {
                  wx.showToast({
                    title: '请输入取消原因',
                    icon: 'none'
                  });
                }
              }
            }
          });
        } else {
          callback(selectedReason);
        }
      }
    });
  },

  // 取消订单
  async cancelOrder(e) {
    const orderId = e.detail.orderId;

    this.showCancelReasonSelector(async (reason) => {
      wx.showModal({
        title: '确认取消',
        content: `确定要取消这个订单吗？\n取消原因：${reason}`,
        success: async (modalRes) => {
          if (modalRes.confirm) {
            try {
              wx.showLoading({
                title: '取消中...',
                mask: true
              });

              // 检查是否为演示模式
              const userInfo = app.globalData.userInfo;
              if (userInfo && userInfo.isDemo) {
                // 演示模式：模拟取消成功
                setTimeout(() => {
                  wx.hideLoading();
                  wx.showToast({
                    title: '订单已取消',
                    icon: 'success'
                  });
                  this.loadLatestOrders();
                }, 1000);
                return;
              }

              // 确定用户角色
              const order = this.data.latestOrders.find(item => item._id === orderId);
              let userRole = 'customer'; // 默认为发单者

              if (order && userInfo) {
                // 如果当前用户是接单者
                if (order.accepterId === userInfo._id) {
                  userRole = 'accepter';
                }
                // 如果当前用户是发单者（通过openid比较）
                else if (order.customerInfo && order.customerInfo.openid === userInfo.openid) {
                  userRole = 'customer';
                }
              }

              console.log('🔍 [取消订单] 用户角色判断:', {
                orderId,
                currentUserId: userInfo?._id,
                currentUserOpenid: userInfo?.openid,
                orderAccepterId: order?.accepterId,
                orderCustomerOpenid: order?.customerInfo?.openid,
                determinedRole: userRole
              });

              // 正常模式：调用取消订单API
              console.log('🚀 [取消订单] 准备调用API:', { orderId, reason, userRole });
              const result = await API.cancelOrder(orderId, reason, userRole);
              console.log('📋 [取消订单] API返回结果:', result);

              wx.hideLoading();

              if (result.success) {
                wx.showToast({
                  title: '订单已取消',
                  icon: 'success'
                });
                this.loadLatestOrders();
              } else {
                wx.showToast({
                  title: result.message || '取消失败',
                  icon: 'error'
                });
              }
            } catch (error) {
              wx.hideLoading();
              console.error('取消订单失败:', error);
              wx.showToast({
                title: '取消失败，请重试',
                icon: 'error'
              });
            }
          }
        }
      });
    });
  },

  // 联系接单者
  contactAccepter(e) {
    const orderId = e.detail.orderId;
    const order = this.data.latestOrders.find(item => item._id === orderId);

    if (order && order.accepterInfo) {
      // 检查是否有聊天室ID
      if (order.chatRoomId) {
        wx.navigateTo({
          url: `/chat-package/pages/room/room?roomId=${order.chatRoomId}&orderId=${orderId}`
        });
      } else {
        // 如果没有聊天室ID，跳转到订单详情页面，通过那里创建聊天室
        wx.navigateTo({
          url: `/order-package/pages/detail/detail?id=${orderId}`
        });
        wx.showToast({
          title: '请通过订单详情联系',
          icon: 'none'
        });
      }
    } else {
      wx.showToast({
        title: '接单者信息不存在',
        icon: 'error'
      });
    }
  },

  // 进入聊天
  enterChat(e) {
    const orderId = e.detail.orderId;
    const order = this.data.latestOrders.find(item => item._id === orderId);

    if (order && order.chatRoomId) {
      wx.navigateTo({
        url: `/chat-package/pages/room/room?roomId=${order.chatRoomId}&orderId=${orderId}`
      });
    } else {
      // 如果没有聊天室，尝试创建或进入聊天
      wx.navigateTo({
        url: `/chat-package/pages/room/room?orderId=${orderId}`
      });
    }
  },

  // 评价订单
  evaluateOrder(e) {
    const orderId = e.detail.orderId;
    wx.navigateTo({
      url: `/order-package/pages/evaluation/evaluation?orderId=${orderId}`
    });
  },

  // 更新订单状态（演示模式用）
  updateOrderStatus(orderId, newStatus) {
    const orders = this.data.latestOrders.map(order => {
      if (order._id === orderId) {
        return {
          ...order,
          status: newStatus,
          statusName: ORDER_STATUS_NAMES[newStatus] || newStatus
        };
      }
      return order;
    });

    this.setData({
      latestOrders: orders
    });
  },

  // 格式化时间
  formatTime(dateStr) {
    // 🔧 彻底修复：异常情况返回空字符串，不显示任何内容
    if (!dateStr) {
      console.log('🕐 [首页] 空时间值，返回空字符串');
      return ''; // 空值时不显示任何内容
    }

    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      console.warn('🕐 [首页] Invalid date:', dateStr, '返回空字符串');
      return ''; // 无效日期时不显示任何内容
    }

    // 🔧 修复时区一致性问题：
    // 存储的时间是UTC时间，需要转换为中国时区时间进行显示
    const now = new Date();
    const chinaOffset = 8 * 60; // 中国时区偏移量（分钟）
    const chinaTime = new Date(now.getTime() + chinaOffset * 60 * 1000);

    // 如果传入的是ISO字符串（UTC时间），需要转换为中国时区时间
    let displayDate = date;
    if (typeof dateStr === 'string' && dateStr.includes('T') && dateStr.includes('Z')) {
      // 这是UTC时间字符串，转换为中国时区时间进行显示
      displayDate = new Date(date.getTime() + chinaOffset * 60 * 1000);
    }

    const diff = chinaTime - displayDate;

    // 处理负时间差（未来时间）
    if (diff < 0) {
      return '刚刚';
    }

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return `${Math.floor(diff / 86400000)}天前`;
    }
  },

  // 确保用户信息已加载
  async ensureUserInfoLoaded() {
    const maxRetries = 10;
    let retries = 0;

    while (retries < maxRetries) {
      const userInfo = app.globalData.userInfo;
      if (userInfo && userInfo.openid) {
        console.log('✅ [首页用户信息] 用户信息已准备就绪:', userInfo.openid);
        return;
      }

      console.log(`⏳ [首页用户信息] 等待用户信息加载... (${retries + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 100));
      retries++;
    }

    console.warn('⚠️ [首页用户信息] 用户信息加载超时，继续执行');
  },

  // 判断是否为订单发布者
  isOrderOwner(order) {
    const app = getApp();
    const userInfo = app.globalData.userInfo;

    console.log('🔍 [首页权限判断] isOrderOwner - 调试信息:', {
      currentUserOpenid: userInfo?.openid,
      orderCustomerOpenid: order.customerInfo?.openid,
      orderId: order._id,
      hasUserInfo: !!userInfo,
      hasOrderCustomerInfo: !!order.customerInfo,
      orderTitle: order.title
    });

    // 如果用户信息或订单客户信息不完整，返回false（显示抢单按钮）
    if (!userInfo || !userInfo.openid || !order.customerInfo || !order.customerInfo.openid) {
      console.log('🔍 [首页权限判断] 缺少必要的用户信息，默认不是订单发布者');
      return false;
    }

    const isOwner = userInfo.openid === order.customerInfo.openid;
    console.log('🔍 [首页权限判断] 是否为订单发布者:', isOwner, '订单:', order.title);
    return isOwner;
  },

  // 导航到创建订单页面
  navigateToCreateOrder() {
    wx.navigateTo({
      url: '/order-package/pages/create/create'
    });
  },







  // 导航到抢单大厅
  navigateToOrderList() {
    console.log('🔄 navigateToOrderList 被调用');

    // 由于订单列表页面在tabBar中，需要使用switchTab
    // 先设置全局标记，让订单列表页面知道要显示抢单大厅模式
    const app = getApp();
    app.globalData.enterGrabMode = true;

    wx.switchTab({
      url: '/pages/order/list/list',
      success: function(res) {
        console.log('✅ 导航到抢单大厅成功', res);
      },
      fail: function(err) {
        console.error('❌ 导航到抢单大厅失败', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 导航到我的订单
  navigateToMyOrders() {
    console.log('🔄 navigateToMyOrders 被调用');

    // 由于订单列表页面在tabBar中，需要使用switchTab
    wx.switchTab({
      url: '/pages/order/list/list',
      success: function(res) {
        console.log('✅ 导航到我的订单成功', res);

        // 跳转成功后，通知页面切换到个人订单模式
        setTimeout(() => {
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          if (currentPage && currentPage.setPersonalMode) {
            console.log('🔄 首页 - 设置个人订单模式');
            currentPage.setPersonalMode();
          }
        }, 100); // 稍微延迟确保页面已经加载
      },
      fail: function(err) {
        console.error('❌ 导航到我的订单失败', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 根据状态导航到我的订单
  navigateToMyOrdersByStatus(e) {
    const status = e.currentTarget.dataset.status;
    console.log('🔄 navigateToMyOrdersByStatus 被调用，状态:', status);

    // 如果是已完成状态，跳转到专门的已完成订单页面
    if (status === 'completed') {
      wx.navigateTo({
        url: '/order-package/pages/completed/completed',
        success: function(res) {
          console.log('✅ 导航到已完成订单页面成功', res);
        },
        fail: function(err) {
          console.error('❌ 导航到已完成订单页面失败', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });
      return;
    }

    // 其他状态：由于订单列表页面在tabBar中，需要使用switchTab
    wx.switchTab({
      url: '/pages/order/list/list',
      success: function(res) {
        console.log('✅ 按状态导航到我的订单成功', res);

        // 跳转成功后，通知页面切换到个人订单模式并设置状态
        setTimeout(() => {
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          if (currentPage && currentPage.setPersonalMode) {
            console.log('🔄 首页 - 设置个人订单模式，状态:', status);
            currentPage.setPersonalMode();

            // 如果页面有切换状态的方法，调用它
            if (currentPage.switchStatus) {
              setTimeout(() => {
                currentPage.switchStatus({ currentTarget: { dataset: { status } } });
              }, 200);
            }
          }
        }, 100);
      },
      fail: function(err) {
        console.error('❌ 按状态导航到我的订单失败', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },



  // 设置背景图片
  async setBackgroundImage() {
    try {
      const backgroundUrl = 'cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/backgrounds/tech-grid.jpg';

      // 确保隐藏系统loading
      wx.hideLoading();

      // 获取临时链接
      const result = await wx.cloud.getTempFileURL({
        fileList: [backgroundUrl]
      });

      // 立即隐藏可能出现的系统loading
      wx.hideLoading();

      if (result.fileList && result.fileList[0]) {
        const fileInfo = result.fileList[0];

        if (fileInfo.status === 0) {
          const tempUrl = fileInfo.tempFileURL;

          // 使用临时链接设置背景
          this.setData({
            backgroundImage: tempUrl,
            hasBackgroundImage: true
          });

        } else {
          this.setData({
            hasBackgroundImage: false
          });
        }
      } else {
        this.setData({
          hasBackgroundImage: false
        });
      }
    } catch (error) {
      console.error('❌ 设置背景图片失败:', error);
      this.setData({
        hasBackgroundImage: false
      });
    }
  },

  // 启动订单实时监听
  startOrderWatcher() {
    const watcherId = 'homepage-orders';

    if (this.data.isWatcherActive) {
      console.log('🚀 [首页实时监听] 监听器已活跃，跳过启动');
      return;
    }

    // 检查云开发是否已初始化
    if (!app.globalData.cloudReady) {
      console.log('⚠️ [首页实时监听] 云开发未就绪，延迟启动监听器');
      setTimeout(() => {
        this.startOrderWatcher();
      }, 2000);
      return;
    }

    console.log('🔄 [首页实时监听] 启动订单监听器');

    // 使用状态保护创建监听器
    const watcher = watcherStateGuard.wrapWatcher(watcherId, () => {
      const db = wx.cloud.database();

      return db.collection('orders')
        .where({ status: 'pending' }) // 只监听待接单的订单
        .orderBy('createTime', 'desc')
        .limit(200)
        .watch({
          onChange: (snapshot) => {
            // 确保页面仍然存在且监听器仍然活跃
            if (this.data && this.data.isWatcherActive) {
              console.log('📡 [首页实时监听] 收到变更事件:', snapshot.type, 'docChanges:', snapshot.docChanges?.length);
              this.handleOrderChange(snapshot);
            }
          },
          onError: (error) => {
            console.error('❌ [首页实时监听] 订单监听器错误:', error);

            // 使用状态保护处理错误
            const isStateConflict = watcherStateGuard.handleError(watcherId, error);

            // 更新本地状态
            if (this.data) {
              this.setData({
                isWatcherActive: false,
                orderWatcher: null
              });
            }

            // 如果不是状态冲突错误，尝试重连
            if (!isStateConflict) {
              setTimeout(() => {
                if (this.data && !this.data.isWatcherActive) {
                  this.startOrderWatcher();
                }
              }, 3000);
            }
          }
        });
    });

    if (watcher) {
      // 更新本地状态
      this.setData({
        orderWatcher: watcher,
        isWatcherActive: true,
        reconnectAttempts: 0
      });

      // 标记状态保护为活跃
      watcherStateGuard.markActive(watcherId, watcher);

      console.log('✅ [首页实时监听] 订单监听器启动成功');
      console.log('✅ [首页实时监听] 最终状态:', {
        isWatcherActive: this.data.isWatcherActive,
        hasWatcher: !!this.data.orderWatcher
      });
    } else {
      console.error('❌ [首页实时监听] 启动订单监听器失败');
      this.setData({
        isWatcherActive: false,
        orderWatcher: null
      });
    }
  },

  // 停止订单实时监听
  stopOrderWatcher() {
    const watcherId = 'homepage-orders';
    console.log('🛑 [首页实时监听] 停止订单监听器');

    // 关闭监听器
    if (this.data.orderWatcher) {
      try {
        this.data.orderWatcher.close();
      } catch (error) {
        console.error('❌ [首页实时监听] 关闭监听器失败:', error);
      }
    }

    // 标记状态保护为非活跃
    watcherStateGuard.markInactive(watcherId);

    // 清除轮询定时器
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer);
      console.log('🛑 [首页轮询] 停止轮询定时器');
    }

    // 清除自动刷新定时器
    if (this.data.autoRefreshTimer) {
      clearInterval(this.data.autoRefreshTimer);
      console.log('🛑 [首页自动刷新] 停止自动刷新定时器');
    }

    // 重置本地状态
    this.setData({
      orderWatcher: null,
      isWatcherActive: false,
      reconnectAttempts: 0,
      reconnectTimer: null,
      pollingTimer: null,
      autoRefreshTimer: null
    });
  },

  // 调度重连
  scheduleReconnect() {
    if (this.data.reconnectAttempts >= this.data.maxReconnectAttempts) {
      console.error('❌ [首页实时监听] 重连次数已达上限，停止重连');
      // 重连失败后，可以考虑降级到定时刷新模式
      this.fallbackToPollingMode();
      return;
    }

    // 清除之前的重连定时器
    if (this.data.reconnectTimer) {
      clearTimeout(this.data.reconnectTimer);
    }

    // 使用指数退避算法计算延迟时间
    const delay = Math.min(1000 * Math.pow(2, this.data.reconnectAttempts), 15000); // 首页最大15秒

    console.log(`🔄 [首页实时监听] 计划在 ${delay}ms 后进行第 ${this.data.reconnectAttempts + 1} 次重连`);

    const timer = setTimeout(() => {
      if (this.data && !this.data.isWatcherActive) {
        this.setData({
          reconnectAttempts: this.data.reconnectAttempts + 1
        });
        console.log(`🔄 [首页实时监听] 开始第 ${this.data.reconnectAttempts} 次重连尝试`);
        this.startOrderWatcher();
      }
    }, delay);

    this.setData({
      reconnectTimer: timer
    });
  },

  // 降级到轮询模式
  fallbackToPollingMode() {
    console.log('🔄 [首页实时监听] 降级到轮询模式');

    // 设置定时刷新，每30秒刷新一次数据
    const pollingTimer = setInterval(() => {
      if (this.data && !this.data.isWatcherActive) {
        console.log('🔄 [首页轮询] 定时刷新订单数据');
        this.loadLatestOrders();
      } else {
        // 如果实时监听恢复了，清除轮询
        clearInterval(pollingTimer);
        console.log('✅ [首页轮询] 实时监听已恢复，停止轮询');
      }
    }, 30000);

    // 保存轮询定时器引用
    this.setData({
      pollingTimer: pollingTimer
    });
  },

  // 启动抢单大厅自动刷新机制
  startAutoRefresh() {
    // 如果已经有自动刷新定时器在运行，先清除
    if (this.data.autoRefreshTimer) {
      clearInterval(this.data.autoRefreshTimer);
      console.log('🔄 [首页自动刷新] 清除现有自动刷新定时器');
    }

    console.log('🚀 [首页自动刷新] 启动抢单大厅自动刷新机制，每1分钟更新一次');

    // 设置1分钟自动刷新定时器
    const autoRefreshTimer = setInterval(() => {
      // 检查页面是否仍然存在且可见
      if (this.data && !this.data.loading) {
        console.log('🔄 [首页自动刷新] 执行定时刷新抢单大厅数据');
        this.refreshOrdersQuietly();
      }
    }, 60000); // 60秒 = 1分钟

    // 保存定时器引用
    this.setData({
      autoRefreshTimer: autoRefreshTimer
    });
  },

  // 停止自动刷新机制
  stopAutoRefresh() {
    if (this.data.autoRefreshTimer) {
      clearInterval(this.data.autoRefreshTimer);
      console.log('🛑 [首页自动刷新] 停止自动刷新定时器');
      this.setData({
        autoRefreshTimer: null
      });
    }
  },

  // 静默刷新订单数据（不显示加载状态）
  async refreshOrdersQuietly() {
    try {
      console.log('🔄 [首页自动刷新] 开始静默刷新订单数据');

      // 调用抢单大厅API获取最新数据
      const result = await API.getGrabOrderList({
        page: 1,
        pageSize: 5
      }, { showLoading: false });

      if (result.success && result.data && result.data.list) {
        const newOrders = result.data.list.map(order => {
          return this.formatOrderData(order);
        });

        // 比较新旧数据，只有在有变化时才更新
        const currentOrders = this.data.latestOrders || [];
        const hasChanges = this.hasOrderListChanged(currentOrders, newOrders);

        if (hasChanges) {
          console.log('🔄 [首页自动刷新] 检测到订单数据变化，更新列表');
          this.setData({
            latestOrders: newOrders
          });

          // 🔧 暂时禁用静默补充，避免破坏时间数据
          // setTimeout(() => {
          //   this.enrichOrdersDataSilently(newOrders);
          // }, 100);
          console.log('🔧 [自动刷新] 跳过静默补充，保持时间数据稳定');

          // 显示更新提示（可选）
          if (newOrders.length > currentOrders.length) {
            const newCount = newOrders.length - currentOrders.length;
            console.log(`🆕 [首页自动刷新] 发现${newCount}个新订单`);
            // 可以在这里添加轻量级的新订单提示
          }
        } else {
          console.log('🔄 [首页自动刷新] 订单数据无变化，跳过更新');
        }
      }
    } catch (error) {
      console.error('❌ [首页自动刷新] 静默刷新失败:', error);
      // 静默失败，不影响用户体验
    }
  },

  // 检查订单列表是否有变化
  hasOrderListChanged(oldOrders, newOrders) {
    // 如果数量不同，肯定有变化
    if (oldOrders.length !== newOrders.length) {
      return true;
    }

    // 检查每个订单的关键字段是否有变化
    for (let i = 0; i < oldOrders.length; i++) {
      const oldOrder = oldOrders[i];
      const newOrder = newOrders[i];

      // 比较关键字段
      if (oldOrder._id !== newOrder._id ||
          oldOrder.status !== newOrder.status ||
          oldOrder.updateTime !== newOrder.updateTime) {
        return true;
      }
    }

    return false;
  },

  // 处理订单变更事件（使用docChanges精确处理）
  handleOrderChange(snapshot) {
    // 只记录有实际变更的事件
    if (snapshot.docChanges && snapshot.docChanges.length > 0) {
      console.log('📡 [首页实时监听] 收到订单变更事件:', snapshot.type, '变更数量:', snapshot.docChanges.length);
    }

    // 使用 docChanges 精确处理变更
    if (snapshot.docChanges && snapshot.docChanges.length > 0) {
      snapshot.docChanges.forEach(change => {
        // 只记录重要的变更，减少日志冗余
        if (change.doc.status === 'pending' || change.changeType === 'add') {
          console.log('📡 [首页实时监听] 处理变更:', change.changeType, change.doc._id, '状态:', change.doc.status);
          console.log('📡 [首页实时监听] 变更数据详情:', {
            customerId: change.doc.customerId,
            customerOpenid: change.doc.customerOpenid,
            title: change.doc.title,
            hasCustomerInfo: !!change.doc.customerInfo
          });
        }

        switch (change.changeType) {
          case 'add':
            // 新增订单 - 只有pending状态的订单才显示在首页
            if (change.doc.status === 'pending') {
              this.handleOrderAdd(change.doc);
            }
            break;

          case 'update':
            // 订单更新 - 检查状态变更
            console.log('📡 [首页实时监听] 订单更新事件，检查状态变更');
            this.handleOrderUpdate(change.doc);
            break;

          case 'remove':
            // 订单删除或状态变更导致的移除 - 从列表中移除
            console.log('📡 [首页实时监听] 订单移除事件');
            this.handleOrderRemove(change.doc);
            break;

          case undefined:
          case null:
            // 处理 changeType 为 undefined 的情况（微信小程序可能的API差异）
            // 检查订单是否已在列表中
            const currentList = this.data.latestOrders || [];
            const existingOrder = currentList.find(order => order._id === change.doc._id);

            // 只为重要操作记录日志
            if (change.doc.status === 'pending' || existingOrder) {
              console.log('🔥 [首页实时监听] changeType为空，订单状态:', change.doc.status, '订单ID:', change.doc._id, '在列表中:', !!existingOrder);
              console.log('🔥 [首页实时监听] 订单详细信息:', {
                customerId: change.doc.customerId,
                customerOpenid: change.doc.customerOpenid,
                _openid: change.doc._openid,
                hasCustomerInfo: !!change.doc.customerInfo,
                customerInfoOpenid: change.doc.customerInfo?.openid
              });
            }

            if (change.doc.status === 'pending') {
              if (!existingOrder) {
                console.log('🔥 [首页实时监听] 新pending订单，添加到列表');
                this.handleOrderAdd(change.doc);
              } else {
                console.log('🔥 [首页实时监听] pending订单已存在，更新信息');
                this.handleOrderUpdate(change.doc);
              }
            } else {
              // 🔥 关键修复：非pending状态，从列表中移除
              if (existingOrder) {
                console.log('🔥 [首页实时监听] 订单状态变更为非pending，从列表移除:', change.doc._id, change.doc.status);
                this.handleOrderRemove(change.doc);
              }
              // 不再记录"无需处理"的日志，减少冗余输出
            }
            break;

          default:
            console.log('📡 [首页实时监听] 未知变更类型:', change.changeType);
            break;
        }
      });
    } else {
      // 兼容旧版本处理方式
      switch (snapshot.type) {
        case 'init':
          console.log('📡 [首页实时监听] 初始化数据');
          break;
        case 'update':
          console.log('📡 [首页实时监听] 订单更新事件');
          this.handleOrderUpdateLegacy(snapshot);
          break;
        case 'add':
          console.log('📡 [首页实时监听] 新增订单事件');
          this.handleOrderAddLegacy(snapshot);
          break;
        case 'remove':
          console.log('📡 [首页实时监听] 订单删除事件');
          this.handleOrderRemoveLegacy(snapshot);
          break;
        default:
          console.log('📡 [首页实时监听] 未知变更类型:', snapshot.type);
          break;
      }
    }
  },

  // 处理订单更新事件（新版本 - 使用单个订单）
  handleOrderUpdate(updatedOrder) {
    console.log('📡 [首页实时监听] 订单更新:', updatedOrder._id, '状态:', updatedOrder.status);

    // 获取当前订单列表
    const currentList = this.data.latestOrders || [];
    const existingOrder = currentList.find(order => order._id === updatedOrder._id);

    console.log('📡 [首页实时监听] 订单在首页列表中:', !!existingOrder, '当前列表长度:', currentList.length);

    if (updatedOrder.status === 'pending') {
      // 如果订单状态是pending，检查是否需要添加到列表
      if (!existingOrder) {
        console.log('📡 [首页实时监听] 订单状态变更为pending，添加到首页:', updatedOrder._id);
        this.handleOrderAdd(updatedOrder);
      } else {
        // 如果已存在，更新订单信息
        console.log('📡 [首页实时监听] 更新现有订单信息:', updatedOrder._id);
        this.updateOrderInList(updatedOrder);
      }
    } else {
      // 🔥 关键修复：如果订单状态不是pending，从列表中移除
      if (existingOrder) {
        console.log('🔥 [首页实时监听] 关键状态变更检测到！订单状态从pending变更为:', updatedOrder.status);
        console.log('🔥 [首页实时监听] 立即从首页移除订单:', updatedOrder._id);
        this.removeOrderFromList(updatedOrder._id);

        // 添加额外的确认日志
        setTimeout(() => {
          const newList = this.data.latestOrders || [];
          const stillExists = newList.find(order => order._id === updatedOrder._id);
          console.log('🔥 [首页实时监听] 移除确认 - 订单是否仍在列表中:', !!stillExists);
        }, 100);
      } else {
        console.log('📡 [首页实时监听] 订单不在首页列表中，无需移除:', updatedOrder._id, updatedOrder.status);
      }
    }
  },

  // 更新列表中的订单信息
  updateOrderInList(updatedOrder) {
    try {
      console.log('📡 [首页实时监听] 开始更新订单信息:', updatedOrder._id);

      // 获取当前订单列表
      const currentList = this.data.latestOrders || [];

      // 更新对应的订单
      const updatedList = currentList.map(order => {
        if (order._id === updatedOrder._id) {
          // 保留原有的customerInfo和权限信息，只更新核心字段
          console.log('📡 [首页实时监听] 保留原有权限信息，更新订单数据');
          console.log('📡 [首页实时监听] 更新前createTime:', {
            value: order.createTime,
            type: typeof order.createTime,
            isDate: order.createTime instanceof Date
          });
          console.log('📡 [首页实时监听] 新updateTime:', {
            value: updatedOrder.updateTime,
            type: typeof updatedOrder.updateTime,
            isDate: updatedOrder.updateTime instanceof Date
          });
          return {
            ...order, // 保留原有的所有字段
            // 只更新可能变化的核心字段
            status: updatedOrder.status,
            title: updatedOrder.title,
            content: updatedOrder.content,
            reward: updatedOrder.reward,
            platformType: updatedOrder.platformType, // 🔥 添加平台类型字段
            serviceType: updatedOrder.serviceType,   // 🔥 添加服务类型字段
            duration: updatedOrder.duration,         // 🔥 添加时长字段
            rounds: updatedOrder.rounds,             // 🔥 添加局数字段
            tags: updatedOrder.tags,                 // 🔥 添加标签字段
            // 🔧 保持原始updateTime，不进行格式化避免破坏有效数据
            updateTime: updatedOrder.updateTime,
            // 确保权限信息不被覆盖
            customerInfo: order.customerInfo,
            isOwner: order.isOwner,
            canGrab: order.canGrab,
            isGrabOrder: order.isGrabOrder
          };
        }
        return order;
      });

      // 更新列表
      this.setData({
        latestOrders: updatedList
      });

      console.log('✅ [首页实时监听] 订单信息已更新:', updatedOrder._id);

    } catch (error) {
      console.error('❌ [首页实时监听] 更新订单信息失败:', error);
    }
  },

  // 处理新增订单事件（新版本 - 使用单个订单）
  handleOrderAdd(newOrder) {
    try {
      // 获取当前订单列表
      const currentList = this.data.latestOrders || [];

      // 检查订单是否已存在（避免重复添加）
      const existingOrder = currentList.find(order => order._id === newOrder._id);
      if (existingOrder) {
        console.log('📡 [首页实时监听] 订单已存在，跳过添加:', newOrder._id);
        return;
      }

      // 直接格式化新订单（使用监听器返回的完整数据，无延迟）
      const formattedOrder = this.formatNewOrderDataInstantly(newOrder);

      // 静默插入到列表顶部，但限制首页显示数量
      const updatedList = [formattedOrder, ...currentList].slice(0, 5); // 首页只显示最新5个订单

      // 立即更新列表，无任何加载状态
      console.log('🔍 [首页实时监听] setData前的formattedOrder.createTime:', {
        createTime: formattedOrder.createTime,
        type: typeof formattedOrder.createTime,
        isDate: formattedOrder.createTime instanceof Date
      });

      this.setData({
        latestOrders: updatedList
      });

      console.log('✅ [首页实时监听] 新订单已静默添加到首页顶部:', newOrder._id);

      // 异步查询用户信息并更新权限（如果订单没有openid信息）
      if (!newOrder.customerOpenid && !newOrder._openid) {
        this.updateOrderPermissionAsync(newOrder._id, newOrder.customerId);
      }

      // 注意：不再主动显示新订单提示，保持无感知更新

    } catch (error) {
      console.error('❌ [首页实时监听] 添加新订单失败:', error);
    }
  },

  // 异步更新订单权限信息
  async updateOrderPermissionAsync(orderId, customerId) {
    try {
      console.log('🔍 [首页权限更新] 开始异步查询用户信息:', orderId, customerId);
      console.log('🔍 [首页权限更新] 当前订单列表长度:', this.data.latestOrders?.length);

      // 查询用户的完整信息（包括昵称、头像等）
      const db = wx.cloud.database();
      const userResult = await db.collection('users')
        .where({ _id: customerId })
        .field({ _id: true, openid: true, nickName: true, avatarUrl: true })
        .get();

      if (userResult.data.length === 0) {
        console.log('🔍 [首页权限更新] 未找到用户信息:', customerId);
        return;
      }

      const customerData = userResult.data[0];
      const customerOpenid = customerData.openid;

      console.log('🔍 [首页权限更新] 查询到的用户数据:', {
        _id: customerData._id,
        nickName: customerData.nickName,
        hasAvatarUrl: !!customerData.avatarUrl,
        hasOpenid: !!customerOpenid
      });

      if (!customerOpenid) {
        console.log('🔍 [首页权限更新] 用户没有openid信息:', customerId);
        return;
      }

      // 获取当前用户信息
      const app = getApp();
      const currentUserInfo = app.globalData.userInfo;
      if (!currentUserInfo || !currentUserInfo.openid) {
        console.log('🔍 [首页权限更新] 当前用户信息不完整');
        return;
      }

      // 计算是否为订单发布者
      const isOwner = currentUserInfo.openid === customerOpenid;

      console.log('🔍 [首页权限更新] 权限计算结果:', {
        orderId,
        currentUserOpenid: currentUserInfo.openid,
        customerOpenid,
        isOwner
      });

      // 更新订单列表中的权限信息
      const currentList = this.data.latestOrders || [];
      const updatedList = currentList.map(order => {
        if (order._id === orderId) {
          return {
            ...order,
            customerInfo: {
              ...order.customerInfo,
              _id: customerData._id,
              nickName: customerData.nickName || order.customerInfo.nickName || '未知用户',
              avatarUrl: customerData.avatarUrl || order.customerInfo.avatarUrl || '',
              openid: customerOpenid
            },
            isOwner: isOwner,
            canGrab: !isOwner,
            isGrabOrder: !isOwner
          };
        }
        return order;
      });

      // 更新页面数据
      this.setData({
        latestOrders: updatedList
      });

      console.log('✅ [首页权限更新] 订单权限已更新:', orderId, '是否为发布者:', isOwner);
      console.log('✅ [首页权限更新] 更新后的用户信息:', {
        nickName: customerData.nickName,
        avatarUrl: customerData.avatarUrl,
        openid: customerOpenid
      });

    } catch (error) {
      console.error('❌ [首页权限更新] 异步更新权限失败:', error);
    }
  },

  // 处理订单删除事件（新版本 - 使用单个订单）
  handleOrderRemove(removedOrder) {
    console.log('📡 [首页实时监听] 订单删除或移除:', removedOrder._id, '状态:', removedOrder.status);

    // 检查订单是否在当前列表中
    const currentList = this.data.latestOrders || [];
    const existingOrder = currentList.find(order => order._id === removedOrder._id);

    if (existingOrder) {
      console.log('📡 [首页实时监听] 从首页静默移除订单:', removedOrder._id);

      // 静默从列表中移除订单，不显示任何提示
      this.removeOrderFromList(removedOrder._id);

      console.log('✅ [首页实时监听] 订单已静默移除，无提示显示');
    } else {
      console.log('📡 [首页实时监听] 订单不在当前列表中，无需移除:', removedOrder._id);
    }
  },

  // 从列表中移除单个订单
  removeOrderFromList(orderId) {
    try {
      console.log('🔥 [首页实时监听] 开始移除订单:', orderId);

      // 获取当前订单列表
      const currentList = this.data.latestOrders || [];
      console.log('🔥 [首页实时监听] 移除前列表长度:', currentList.length);

      // 检查订单是否确实在列表中
      const targetOrder = currentList.find(order => order._id === orderId);
      if (!targetOrder) {
        console.log('🔥 [首页实时监听] 警告：要移除的订单不在列表中:', orderId);
        return;
      }

      console.log('🔥 [首页实时监听] 找到目标订单:', targetOrder.title, '状态:', targetOrder.status);

      // 过滤掉要移除的订单
      const updatedList = currentList.filter(order => order._id !== orderId);
      console.log('🔥 [首页实时监听] 移除后列表长度:', updatedList.length);

      // 更新列表
      this.setData({
        latestOrders: updatedList
      });

      console.log('✅ [首页实时监听] 订单已从首页列表移除:', orderId);
      console.log('✅ [首页实时监听] 当前首页订单数量:', updatedList.length);

    } catch (error) {
      console.error('❌ [首页实时监听] 移除订单失败:', error);
    }
  },

  // 简化的新订单格式化方法（无延迟）
  formatNewOrderDataInstantly(order) {
    console.log('🔍 [首页实时格式化] 原始订单数据:', order);
    console.log('🔍 [首页实时格式化] 原始platformType字段:', order.platformType);
    console.log('🔍 [首页实时格式化] 订单时间信息:', {
      createTime: order.createTime,
      createTimeType: typeof order.createTime,
      isDate: order.createTime instanceof Date,
      updateTime: order.updateTime,
      updateTimeType: typeof order.updateTime
    });
    console.log('🔍 [首页实时格式化] 订单用户信息:', {
      customerId: order.customerId,
      customerOpenid: order.customerOpenid,
      _openid: order._openid,
      hasCustomerInfo: !!order.customerInfo,
      customerInfoOpenid: order.customerInfo?.openid
    });

    // 如果实时监听数据中缺少platformType，尝试从本地存储获取
    let platformType = order.platformType;
    if (!platformType) {
      try {
        const storedData = wx.getStorageSync(`create_order_${order._id}`);
        if (storedData && storedData.platformType) {
          platformType = storedData.platformType;
          console.log('🔍 [首页实时格式化] 从本地存储获取platformType:', platformType);
        }
      } catch (error) {
        console.warn('读取本地存储platformType失败:', error);
      }
    }

    const result = {
      ...order,
      // 确保platformType字段存在，优先使用原始数据
      platformType: order.platformType || platformType || 'pc',
      // 基本显示信息
      statusText: ORDER_STATUS_NAMES[order.status] || order.status,
      createTimeText: this.formatTime(order.createTime),
      // 🔧 保持原始时间字段，避免格式化破坏有效数据
      createTime: order.createTime instanceof Date ? order.createTime.toISOString() : order.createTime,
      updateTime: order.updateTime instanceof Date ? order.updateTime.toISOString() : order.updateTime,

      // 清理文本内容
      content: this.cleanTextContent(order.content),
      title: this.cleanTextContent(order.title),

      // 游戏信息
      gameInfo: order.gameInfo || {
        gameName: this.cleanTextContent(order.title) || '高阶技术指导',
        gameMode: order.gameMode || null
      },

      // 客户信息（使用监听器返回的数据，确保包含openid）
      customerInfo: order.customerInfo || {
        nickName: '未知用户',
        avatarUrl: '',
        _id: order.customerId,
        openid: order.customerOpenid || order._openid || '' // 从订单数据中获取openid
      },

      // 接单者信息
      accepterInfo: order.accepterInfo || null,

      // 显示用的金额
      displayAmount: order.totalAmount || order.reward || 0,

      // 服务时长显示
      durationText: this.formatDuration(order.duration),

      // 标签信息
      tags: order.tags || [],

      // 是否可以抢单（首页显示的都是可抢单的）
      canGrab: true,
      isGrabOrder: true
    };

    // 判断是否为订单发布者（实时格式化也需要这个字段）
    result.isOwner = this.isOrderOwner(result);
    result.canGrab = !result.isOwner;
    result.isGrabOrder = !result.isOwner;

    // 如果缺少用户信息，异步更新权限
    const needsUserInfoUpdate = !result.customerInfo.openid ||
                               !result.customerInfo.nickName ||
                               result.customerInfo.nickName === '未知用户';

    if (needsUserInfoUpdate && order.customerId) {
      console.log('🔍 [首页实时格式化] 需要更新用户信息，将异步更新:', order._id, {
        hasOpenid: !!result.customerInfo.openid,
        nickName: result.customerInfo.nickName
      });
      // 延迟执行异步更新，避免阻塞UI
      setTimeout(() => {
        this.updateOrderPermissionAsync(order._id, order.customerId);
      }, 100);
    }

    console.log('🔍 [首页实时格式化] 最终platformType:', result.platformType);
    console.log('🔍 [首页实时格式化] 局部变量platformType:', platformType);
    console.log('🔍 [首页实时格式化] order.platformType:', order.platformType);
    console.log('🔍 [首页实时格式化] isOwner判断结果:', result.isOwner);
    console.log('🔍 [首页实时格式化] customerInfo.openid:', result.customerInfo.openid);
    console.log('🔍 [首页实时格式化] 最终result的createTime:', {
      createTime: result.createTime,
      type: typeof result.createTime,
      isDate: result.createTime instanceof Date
    });
    return result;
  },

  // 兼容旧版本的处理方法
  handleOrderAddLegacy(snapshot) {
    const newOrders = snapshot.docs;
    if (newOrders.length > 0) {
      console.log('📡 [首页实时监听] 检测到新订单（兼容模式）:', newOrders.length, '个');
      newOrders.forEach(order => {
        // 只处理pending状态的订单
        if (order.status === 'pending') {
          this.handleOrderAdd(order);
        }
      });
    }
  },

  handleOrderUpdateLegacy(snapshot) {
    const updatedOrders = snapshot.docs;
    let needRefresh = false;

    updatedOrders.forEach(order => {
      // 如果订单状态不再是pending，说明被接单了，需要从列表中移除
      if (order.status !== 'pending') {
        console.log('📡 [首页实时监听] 订单状态变更，从首页移除:', order._id, order.status);
        needRefresh = true;
      }
    });

    if (needRefresh) {
      // 重新加载订单列表
      this.loadLatestOrders();
    }
  },

  handleOrderRemoveLegacy(snapshot) {
    const removedOrders = snapshot.docs;
    if (removedOrders.length > 0) {
      console.log('📡 [首页实时监听] 检测到订单删除（兼容模式）:', removedOrders.length, '个');
      removedOrders.forEach(order => this.handleOrderRemove(order));
    }
  },

  // 立即添加新订单到首页列表
  addNewOrdersToHomePageInstantly(newOrders) {
    try {
      console.log('📡 [首页实时监听] 立即添加新订单到首页');

      // 获取当前订单列表
      const currentList = this.data.latestOrders || [];

      // 立即格式化新订单（使用基本信息）
      const formattedNewOrders = newOrders.map(order => {
        // 构建临时的客户信息用于权限判断
        // 注意：实时监听的订单数据可能没有customerOpenid字段，需要通过customerId查询
        const tempCustomerInfo = {
          _id: order.customerId,
          nickName: '加载中...',
          avatarUrl: '',
          openid: order.customerOpenid || order._openid || '' // 可能为空，稍后异步补充
        };

        const tempOrder = {
          ...order,
          customerInfo: tempCustomerInfo
        };

        // 如果没有openid信息，暂时设为false（显示抢单按钮），稍后异步更新
        let isOwner = false;
        if (tempCustomerInfo.openid) {
          isOwner = this.isOrderOwner(tempOrder);
        }

        console.log('📡 [首页实时监听] 新订单权限判断:', {
          orderId: order._id,
          orderTitle: order.title,
          customerOpenid: order.customerOpenid || order._openid || '无',
          hasOpenid: !!tempCustomerInfo.openid,
          isOwner: isOwner
        });

        return {
          ...order,
          customerInfo: tempCustomerInfo,
          totalAmount: order.reward || order.totalAmount || 0,
          isOwner: isOwner,
          canGrab: !isOwner, // 只有非发布者才能抢单
          isGrabOrder: !isOwner, // 只有非发布者才显示抢单按钮
          isLoadingUserInfo: true,
          // 确保时间字段正确格式化
          createTime: order.createTime instanceof Date ? order.createTime.toISOString() : order.createTime,
          updateTime: order.updateTime instanceof Date ? order.updateTime.toISOString() : order.updateTime
        };
      });

      // 将新订单添加到列表开头，限制显示数量
      const updatedList = [...formattedNewOrders, ...currentList].slice(0, 10);

      // 立即更新列表显示
      this.setData({
        latestOrders: updatedList
      });

      console.log('✅ [首页实时监听] 新订单已立即添加到首页，当前订单数:', updatedList.length);

      // 异步补充用户信息
      this.enrichHomePageOrdersWithUserInfo(newOrders);

    } catch (error) {
      console.error('❌ [首页实时监听] 立即添加新订单失败:', error);
      // 如果立即添加失败，回退到重新加载
      this.loadLatestOrders();
    }
  },

  // 异步补充首页订单的用户信息
  async enrichHomePageOrdersWithUserInfo(newOrders) {
    try {
      console.log('📡 [首页实时监听] 开始异步补充用户信息');

      // 获取所有需要的用户ID
      const customerIds = [...new Set(newOrders.map(order => order.customerId))];

      if (customerIds.length === 0) {
        return;
      }

      // 查询用户信息
      const db = wx.cloud.database();
      const _ = db.command;

      const customerResult = await db.collection('users')
        .where({
          _id: _.in(customerIds)
        })
        .field({
          _id: true,
          nickName: true,
          avatarUrl: true
        })
        .get();

      // 创建用户信息映射
      const customerMap = {};
      customerResult.data.forEach(customer => {
        customerMap[customer._id] = customer;
      });

      // 获取当前订单列表
      const currentList = this.data.latestOrders || [];

      // 更新列表中对应订单的用户信息
      const updatedList = currentList.map(existingOrder => {
        // 查找是否是刚添加的新订单
        const newOrderMatch = newOrders.find(newOrder => newOrder._id === existingOrder._id);
        if (newOrderMatch && existingOrder.isLoadingUserInfo) {
          const customer = customerMap[newOrderMatch.customerId] || {};
          // 更新用户信息，移除加载标记
          return {
            ...existingOrder,
            customerInfo: {
              _id: customer._id,
              nickName: customer.nickName || '未知用户',
              avatarUrl: customer.avatarUrl || ''
            },
            isLoadingUserInfo: false
          };
        }
        return existingOrder;
      });

      // 更新列表
      this.setData({
        latestOrders: updatedList
      });

      console.log('✅ [首页实时监听] 用户信息补充完成');

    } catch (error) {
      console.error('❌ [首页实时监听] 补充用户信息失败:', error);
      // 补充用户信息失败不影响订单显示
    }
  },

  // 静默补充订单数据（完全后台处理，不影响用户界面）
  async enrichOrdersDataSilently(orders) {
    if (!orders || orders.length === 0) {
      return; // 没有需要补充的订单
    }

    console.log(`🔧 [静默补充] 开始补充${orders.length}个订单的详细数据`);

    try {
      // 并行补充所有订单的详细信息，禁用所有系统加载提示
      const enrichPromises = orders.map(async (order) => {
        try {
          // 如果订单缺少用户信息，静默获取
          const needsUserInfo = !order.customerInfo ||
                               !order.customerInfo.nickName ||
                               order.customerInfo.nickName === '加载中...' ||
                               order.customerInfo.nickName === '未知用户';

          console.log('🔧 [静默补充] 订单', order._id, '需要补充用户信息:', needsUserInfo, '当前用户信息:', order.customerInfo);

          if (needsUserInfo) {
            const userResult = await API.callFunction('getUserInfo', {
              userId: order.customerId
            }, { showLoading: false });

            if (userResult.success && userResult.data) {
              console.log('🔧 [静默补充] 获取到用户信息:', userResult.data);
              order.customerInfo = {
                _id: userResult.data._id,
                nickName: userResult.data.nickName || '未知用户',
                avatarUrl: userResult.data.avatarUrl || '',
                openid: userResult.data.openid || ''
              };
            } else {
              console.log('🔧 [静默补充] 获取用户信息失败:', userResult);
            }
          }

          // 如果需要其他数据补充，可以在这里添加
          return true;
        } catch (error) {
          console.error('❌ [静默补充] 补充订单数据失败:', order._id, error);
          return false;
        }
      });

      // 等待所有补充完成
      const results = await Promise.all(enrichPromises);
      const successCount = results.filter(r => r).length;

      if (successCount > 0) {
        console.log(`✅ [静默补充] 补充完成，成功补充${successCount}个订单数据`);

        // 🔧 彻底修复时序问题：只更新用户信息，不触碰时间字段
        console.log(`✅ [静默补充] 补充完成，成功补充${successCount}个订单的用户信息`);

        // 🔧 关键修复：只有在有用户信息更新时才进行setData，完全不修改时间字段
        if (successCount > 0) {
          console.log('🔧 [静默补充] 用户信息已更新，执行setData（保持原始时间字段不变）');

          // 直接使用修改后的orders，不重新格式化时间字段
          this.setData({
            latestOrders: [...orders] // 使用浅拷贝，保持时间字段原样
          });
        } else {
          console.log('🔧 [静默补充] 无用户信息更新，跳过setData避免组件重新渲染');
        }
      }
    } catch (error) {
      console.error('❌ [静默补充] 订单数据补充失败:', error);
    }
  },

  // 设置通知事件监听
  setupNotificationListeners() {
    // 监听应用内通知事件
    app.$on('showInAppNotification', this.handleInAppNotification.bind(this));
    console.log('📱 [首页] 通知事件监听已设置');
  },

  // 处理应用内通知
  handleInAppNotification(data) {
    // 标记通知已被处理
    app.notificationHandled = true;

    const { type, title, content, messageData, chatRoomId } = data;

    // 获取发送者头像（如果有）
    let avatar = '';
    if (messageData && messageData.senderInfo) {
      avatar = messageData.senderInfo.avatarUrl || '';
    }

    // 根据通知类型设置不同的显示时长
    let duration = 1500; // 默认1.5秒
    switch (type) {
      case 'message':
        duration = 1500; // 聊天消息显示1.5秒
        break;
      case 'order':
        duration = 1500; // 订单通知显示1.5秒
        break;
      case 'system':
        duration = 1500; // 系统通知显示1.5秒
        break;
      default:
        duration = 1500;
    }

    // 显示通知，保存聊天室ID和消息数据用于跳转
    this.setData({
      notification: {
        show: true,
        type: type || 'message',
        title: title || '',
        content: content || '',
        avatar: avatar,
        duration: duration,
        chatRoomId: chatRoomId, // 保存聊天室ID
        messageData: messageData // 保存消息数据
      }
    });
  },

  // 通知点击事件
  onNotificationTap(e) {
    const { type, title, content, chatRoomId, messageData } = e.detail;

    // 隐藏通知
    this.setData({
      'notification.show': false
    });

    // 根据通知类型进行跳转
    if (type === 'message') {
      // 如果有聊天室ID，直接跳转到对应聊天室
      if (chatRoomId) {
        console.log('📱 [通知跳转] 跳转到聊天室:', chatRoomId);
        wx.navigateTo({
          url: `/chat-package/pages/room/room?roomId=${chatRoomId}`
        });
      } else {
        // 没有聊天室ID，跳转到聊天列表
        console.log('📱 [通知跳转] 跳转到聊天列表');
        wx.switchTab({
          url: '/pages/chat/list/list'
        });
      }
    }
  },

  // 通知隐藏事件
  onNotificationHide() {
    // 不需要再次设置show为false，避免无限循环
    // 通知组件自己会处理visible状态
  },



  // 页面卸载时清理事件监听
  onUnload() {
    // 清理通知事件监听
    app.$off('showInAppNotification', this.handleInAppNotification);

    // 清理其他资源...
    if (this.orderWatcher) {
      this.orderWatcher.close();
      this.orderWatcher = null;
    }

    console.log('📱 [首页] 页面卸载，资源已清理');
  },

  // ==================== 公告相关方法 ====================

  // 加载公告列表
  async loadAnnouncements() {
    try {
      console.log('🔔 [公告] 开始加载公告列表');

      // 检查缓存
      const cacheKey = 'announcement_list_cache';
      const cacheExpiry = 5 * 60 * 1000; // 5分钟缓存

      try {
        const cachedData = wx.getStorageSync(cacheKey);
        if (cachedData && cachedData.timestamp &&
            (Date.now() - cachedData.timestamp < cacheExpiry)) {
          console.log('🔔 [公告] 使用缓存数据');
          this.setData({
            announcements: cachedData.data
          });
          return;
        }
      } catch (cacheError) {
        console.log('🔔 [公告] 缓存读取失败，继续网络请求');
      }

      const result = await wx.cloud.callFunction({
        name: 'getAnnouncementList',
        data: {
          page: 1,
          pageSize: 5, // 首页只显示前5条
          status: 'active',
          isAdmin: false
        }
      });

      if (result.result && result.result.success) {
        const announcements = result.result.data.list || [];
        console.log('🔔 [公告] 加载成功，数量:', announcements.length);

        // 缓存数据
        try {
          wx.setStorageSync(cacheKey, {
            data: announcements,
            timestamp: Date.now()
          });
        } catch (cacheError) {
          console.log('🔔 [公告] 缓存保存失败:', cacheError);
        }

        this.setData({
          announcements: announcements
        });
      } else {
        console.error('🔔 [公告] 加载失败:', result.result?.error);
      }
    } catch (error) {
      console.error('🔔 [公告] 加载异常:', error);
    }
  },

  // 公告点击事件
  onAnnouncementTap(e) {
    const { announcement } = e.detail;
    console.log('🔔 [公告] 点击公告:', announcement.title);

    // 可以在这里添加统计或其他逻辑
    // 详情弹窗已在组件内部处理
  },

  // 关闭公告横幅
  onCloseBanner() {
    console.log('🔔 [公告] 关闭公告横幅');

    // 隐藏公告横幅
    this.setData({
      announcements: []
    });

    // 可以在这里保存用户偏好，下次不显示
    try {
      wx.setStorageSync('announcement_banner_closed', true);
    } catch (error) {
      console.error('保存公告横幅关闭状态失败:', error);
    }
  }

})
