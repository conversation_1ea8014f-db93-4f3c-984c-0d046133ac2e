<!--index.wxml - 科技感首页-->
<navigation-bar title="三角洲接单平台" back="{{false}}" color="#00d4ff" background="rgba(15, 23, 42, 0.95)" loading="{{navLoading}}"></navigation-bar>

<!-- 应用内通知组件 -->
<notification-toast
  show="{{notification.show}}"
  type="{{notification.type}}"
  title="{{notification.title}}"
  content="{{notification.content}}"
  avatar="{{notification.avatar}}"
  duration="{{notification.duration}}"
  chatRoomId="{{notification.chatRoomId}}"
  messageData="{{notification.messageData}}"
  bind:tap="onNotificationTap"
  bind:hide="onNotificationHide"
/>

<!-- 科技感装饰背景 -->
<view class="tech-bg {{hasBackgroundImage ? 'has-custom-bg' : ''}}"
      style="{{backgroundImage ? 'background-image: url(' + backgroundImage + '); background-size: cover; background-position: center; background-repeat: no-repeat;' : ''}}">
  <view class="tech-grid {{hasBackgroundImage ? 'with-overlay' : ''}}"></view>
  <view class="tech-particles">
    <view class="particle" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
  </view>
</view>



<scroll-view class="scrollarea page-with-custom-nav" scroll-y type="list">
  <!-- 主题加载完成后显示内容 -->
  <view class="container" wx:if="{{themeLoaded}}">
    <!-- 公告横幅 -->
    <announcement-banner
      announcements="{{announcements}}"
      show-close-button="{{true}}"
      bind:announcementTap="onAnnouncementTap"
      bind:closeBanner="onCloseBanner"
      class="fade-in"
      style="animation-delay: 0.1s;">
    </announcement-banner>

    <!-- 现代化轮播图 -->
    <view class="fade-in" style="animation-delay: 0.2s;">
      <swiper class="banner-swiper"
              indicator-dots="{{true}}"
              indicator-color="rgba(255,255,255,0.3)"
              indicator-active-color="#ffffff"
              autoplay="{{true}}"
              interval="{{4000}}"
              duration="{{800}}"
              circular="{{true}}"
              easing-function="easeInOutCubic">
        <swiper-item wx:for="{{banners}}" wx:key="index">
          <view class="banner-item">
            <image class="banner-image" src="{{item.image}}" mode="aspectFill" lazy-load="{{true}}" />
            <view class="banner-title" wx:if="{{item.title}}">{{item.title}}</view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 每日密码显示 -->
    <view class="daily-password-card fade-in" style="animation-delay: 0.4s;" wx:if="{{!passwordLoading && dailyPassword.locations.length > 0}}">
      <view class="password-header">
        <view class="password-title">
          <text class="password-icon">🔐</text>
          <text class="password-text">{{dailyPassword.date}}密码已更新</text>
        </view>
        <view class="password-actions">
          <view class="refresh-btn" bindtap="fetchDailyPassword">
            <text class="refresh-icon">🔄</text>
            <text class="refresh-text">刷新</text>
          </view>
          <view class="copy-all-btn" bindtap="copyAllPasswords">
            <text class="copy-icon">📋</text>
            <text class="copy-text">复制全部</text>
          </view>
        </view>
      </view>

      <view class="password-grid">
        <view class="password-item"
              wx:for="{{dailyPassword.locations}}"
              wx:key="name"
              bindtap="copyPassword"
              data-location="{{item.name}}"
              data-code="{{item.code}}">
          <view class="location-name">{{item.name}}</view>
          <view class="location-code">{{item.code}}</view>
          <view class="copy-hint">点击复制</view>
        </view>
      </view>
    </view>

    <!-- 密码破译状态 -->
    <view class="daily-password-card decrypting fade-in" style="animation-delay: 0.4s;" wx:if="{{passwordLoading}}">
      <view class="password-header">
        <view class="password-title">
          <text class="password-icon">🔐</text>
          <text class="password-text">{{dailyPassword.date || '今日'}}密码破译中...</text>
        </view>
        <view class="password-actions">
          <view class="decryption-status">
            <text class="status-dot decrypting"></text>
            <text class="status-text">DECRYPTING</text>
          </view>
        </view>
      </view>

      <view class="password-grid">
        <view class="password-item decrypting-item" wx:for="{{['零号大坝', '长弓溪谷', '巴克什', '航天基地', '潮汐监狱']}}" wx:key="*this">
          <view class="location-name">{{item}}</view>
          <view class="location-code decrypting-code">
            <text class="matrix-char" wx:for="{{[0,1,2,3]}}" wx:key="index">{{matrixChars[index]}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="card fade-in" style="animation-delay: 0.5s;">
      <view class="section-header">
        <text class="card-title">🎯 抢单大厅</text>
        <view class="header-actions">
          <view class="more-link" bindtap="navigateToOrderList">更多 →</view>
        </view>
      </view>
      <view class="order-list">
        <!-- 使用统一的订单卡片组件 -->
        <order-card
          wx:for="{{latestOrders}}"
          wx:key="_id"
          order-data="{{item}}"
          bind:cardtap="navigateToOrderDetail"
          bind:graborder="grabOrder"
          bind:viewdetail="navigateToOrderDetail"
          bind:editorder="editOrder"
          bind:cancelorder="cancelOrder"
          bind:contactaccepter="contactAccepter"
          bind:enterchat="enterChat"
          bind:evaluateorder="evaluateOrder">
        </order-card>

        <!-- 加载状态 - G.T.I. SECURITY 品牌加载器 -->
        <view wx:if="{{loading}}" class="gti-loading-container">
          <view class="gti-logo-container">
            <image class="gti-logo" src="cloud://cloud1-9gsj7t48183e5a9f.636c-cloud1-9gsj7t48183e5a9f-1366958750/images/logos/gti-security-logo.png" mode="aspectFit"></image>
          </view>
          <view class="gti-loading-spinner"></view>
          <text class="gti-loading-text">正在加载抢单大厅...</text>
          <view class="gti-loading-dots">
            <view class="dot dot-1"></view>
            <view class="dot dot-2"></view>
            <view class="dot dot-3"></view>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:elif="{{latestOrders.length === 0}}" class="empty-state">
          <text class="empty-icon">🎯</text>
          <text class="empty-text">暂无可抢订单</text>
          <text class="empty-desc">快去发布订单，等待其他用户抢单吧！</text>
          <button class="empty-action" bindtap="navigateToCreate">立即发单</button>
        </view>
      </view>
    </view>

  </view>
</scroll-view>

<!-- 浮动创建订单按钮 -->
<view class="btn-floating fade-in" style="animation-delay: 0.9s;" bindtap="navigateToCreateOrder" wx:if="{{themeLoaded}}">
  <image wx:if="{{floatingIcon && !iconLoading}}" class="floating-icon" src="{{floatingIcon}}" mode="aspectFit" />
  <text wx:elif="{{iconLoading}}" class="floating-text">⋯</text>
  <text wx:else class="floating-text">+</text>
</view>








