// 清除缓存并重新加载公告的脚本
// 请在微信开发者工具的控制台中运行此代码

console.log('🧹 开始清除缓存并重新加载公告...');

// 清除公告相关缓存
function clearAnnouncementCache() {
  try {
    console.log('🗑️ 清除公告缓存...');
    
    // 清除公告列表缓存
    wx.removeStorageSync('announcement_list_cache');
    console.log('✅ 公告列表缓存已清除');
    
    // 清除公告横幅关闭状态
    wx.removeStorageSync('announcement_banner_closed');
    console.log('✅ 公告横幅关闭状态已清除');
    
    // 清除其他可能的公告相关缓存
    const storage = wx.getStorageInfoSync();
    storage.keys.forEach(key => {
      if (key.includes('announcement')) {
        wx.removeStorageSync(key);
        console.log(`✅ 已清除缓存: ${key}`);
      }
    });
    
  } catch (error) {
    console.error('❌ 清除缓存异常:', error);
  }
}

// 获取当前页面实例
function getCurrentPageInstance() {
  const pages = getCurrentPages();
  return pages[pages.length - 1];
}

// 重新加载公告数据
async function reloadAnnouncements() {
  try {
    console.log('🔄 重新加载公告数据...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此函数');
      return;
    }
    
    // 先清除当前的公告数据
    currentPage.setData({
      announcements: []
    });
    
    console.log('📞 调用 loadAnnouncements 方法...');
    
    // 重新加载公告
    if (typeof currentPage.loadAnnouncements === 'function') {
      await currentPage.loadAnnouncements();
      
      // 等待一下再检查结果
      setTimeout(() => {
        const announcements = currentPage.data.announcements;
        console.log('📊 重新加载后的公告数据:', announcements);
        console.log('📊 公告数量:', announcements ? announcements.length : 0);
        
        if (announcements && announcements.length > 0) {
          console.log('✅ 公告重新加载成功！');
          announcements.forEach((item, index) => {
            console.log(`📄 公告 ${index + 1}: ${item.title}`);
          });
        } else {
          console.log('❌ 公告重新加载后仍然没有数据');
        }
      }, 2000);
      
    } else {
      console.log('❌ 首页没有 loadAnnouncements 方法');
    }
    
  } catch (error) {
    console.error('❌ 重新加载公告异常:', error);
  }
}

// 直接从云函数获取公告数据并设置
async function directLoadFromCloud() {
  try {
    console.log('☁️ 直接从云函数获取公告数据...');
    
    const result = await wx.cloud.callFunction({
      name: 'getAnnouncementList',
      data: {
        page: 1,
        pageSize: 10,
        status: 'active',
        isAdmin: false
      }
    });
    
    console.log('☁️ 云函数返回结果:', result);
    
    if (result.result && result.result.success) {
      const announcements = result.result.data.list || [];
      console.log('✅ 从云函数获取到公告数据，数量:', announcements.length);
      
      // 设置到首页
      const currentPage = getCurrentPageInstance();
      if (currentPage.route === 'pages/index/index') {
        currentPage.setData({
          announcements: announcements
        });
        console.log('✅ 公告数据已设置到首页');
        
        // 验证设置结果
        setTimeout(() => {
          const pageAnnouncements = currentPage.data.announcements;
          console.log('📊 首页公告数据验证:', pageAnnouncements);
          console.log('📊 验证数量:', pageAnnouncements ? pageAnnouncements.length : 0);
        }, 500);
        
      } else {
        console.log('⚠️ 当前不在首页');
      }
      
    } else {
      console.error('❌ 从云函数获取公告失败:', result.result?.error);
    }
    
  } catch (error) {
    console.error('❌ 直接从云函数获取公告异常:', error);
  }
}

// 完整的修复流程
async function fullRepairProcess() {
  try {
    console.log('🔧 开始完整修复流程...');
    
    // 1. 清除缓存
    console.log('步骤 1: 清除缓存');
    clearAnnouncementCache();
    
    // 2. 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 直接从云函数获取数据
    console.log('步骤 2: 直接从云函数获取数据');
    await directLoadFromCloud();
    
    // 4. 等待一下
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 5. 重新加载页面方法
    console.log('步骤 3: 重新加载页面方法');
    await reloadAnnouncements();
    
    console.log('🎉 完整修复流程完成！');
    
  } catch (error) {
    console.error('❌ 完整修复流程异常:', error);
  }
}

// 检查页面状态
function checkPageStatus() {
  try {
    const currentPage = getCurrentPageInstance();
    console.log('📊 页面状态检查:');
    console.log('  - 页面路径:', currentPage.route);
    console.log('  - themeLoaded:', currentPage.data.themeLoaded);
    console.log('  - loading:', currentPage.data.loading);
    console.log('  - announcements 长度:', currentPage.data.announcements ? currentPage.data.announcements.length : 0);
    
    // 检查组件是否存在
    const query = wx.createSelectorQuery();
    query.select('announcement-banner').boundingClientRect();
    query.exec((res) => {
      console.log('📊 announcement-banner 组件查询结果:', res);
    });
    
  } catch (error) {
    console.error('❌ 检查页面状态异常:', error);
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.clearAnnouncementCache = clearAnnouncementCache;
  window.reloadAnnouncements = reloadAnnouncements;
  window.directLoadFromCloud = directLoadFromCloud;
  window.fullRepairProcess = fullRepairProcess;
  window.checkPageStatus = checkPageStatus;
}

console.log('📋 缓存清除和重新加载脚本加载完成！');
console.log('💡 使用方法：');
console.log('  - 清除缓存: clearAnnouncementCache()');
console.log('  - 重新加载公告: reloadAnnouncements()');
console.log('  - 直接从云函数获取: directLoadFromCloud()');
console.log('  - 完整修复流程: fullRepairProcess()');
console.log('  - 检查页面状态: checkPageStatus()');

console.log('🔄 自动开始完整修复流程...');
fullRepairProcess();
