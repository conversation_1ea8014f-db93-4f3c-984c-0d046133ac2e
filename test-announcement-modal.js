// 测试公告弹窗功能的脚本
// 请在微信开发者工具的控制台中运行此代码

console.log('🧪 开始测试公告弹窗功能...');

// 获取当前页面实例
function getCurrentPageInstance() {
  const pages = getCurrentPages();
  return pages[pages.length - 1];
}

// 测试弹窗显示
function testModalDisplay() {
  try {
    console.log('🔍 测试弹窗显示...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此测试');
      return;
    }
    
    // 检查公告数据
    const announcements = currentPage.data.announcements;
    if (!announcements || announcements.length === 0) {
      console.log('❌ 没有公告数据，无法测试弹窗');
      return;
    }
    
    console.log('✅ 找到公告数据，数量:', announcements.length);
    
    // 模拟点击第一个公告
    const firstAnnouncement = announcements[0];
    console.log('📄 测试公告:', firstAnnouncement.title);
    
    // 查找公告组件
    const query = wx.createSelectorQuery();
    query.select('announcement-banner').context();
    query.exec((res) => {
      console.log('📊 公告组件查询结果:', res);
      
      if (res[0] && res[0].context) {
        const component = res[0].context;
        console.log('✅ 找到公告组件');
        
        // 模拟点击事件
        const mockEvent = {
          currentTarget: {
            dataset: {
              announcement: firstAnnouncement
            }
          }
        };
        
        // 调用组件的点击方法
        if (typeof component.onAnnouncementTap === 'function') {
          console.log('📞 调用组件的 onAnnouncementTap 方法...');
          component.onAnnouncementTap(mockEvent);
          
          // 检查弹窗状态
          setTimeout(() => {
            console.log('📊 组件数据状态:', component.data);
            console.log('📊 弹窗显示状态:', component.data.showModal);
            console.log('📊 选中的公告:', component.data.selectedAnnouncement);
          }, 500);
          
        } else {
          console.log('❌ 组件没有 onAnnouncementTap 方法');
        }
        
      } else {
        console.log('❌ 没有找到公告组件');
      }
    });
    
  } catch (error) {
    console.error('❌ 测试弹窗显示异常:', error);
  }
}

// 检查弹窗元素是否存在
function checkModalElements() {
  try {
    console.log('🔍 检查弹窗元素...');
    
    const query = wx.createSelectorQuery();
    
    // 检查弹窗容器
    query.select('.announcement-modal').boundingClientRect();
    
    // 检查弹窗内容
    query.select('.modal-content').boundingClientRect();
    
    // 检查弹窗遮罩
    query.select('.modal-mask').boundingClientRect();
    
    query.exec((res) => {
      console.log('📊 弹窗元素查询结果:', res);
      
      if (res[0]) {
        console.log('✅ 找到弹窗容器元素');
        console.log('📐 弹窗容器位置:', res[0]);
      } else {
        console.log('❌ 没有找到弹窗容器元素');
      }
      
      if (res[1]) {
        console.log('✅ 找到弹窗内容元素');
        console.log('📐 弹窗内容位置:', res[1]);
      } else {
        console.log('❌ 没有找到弹窗内容元素');
      }
      
      if (res[2]) {
        console.log('✅ 找到弹窗遮罩元素');
        console.log('📐 弹窗遮罩位置:', res[2]);
      } else {
        console.log('❌ 没有找到弹窗遮罩元素');
      }
    });
    
  } catch (error) {
    console.error('❌ 检查弹窗元素异常:', error);
  }
}

// 手动触发弹窗显示
function manualShowModal() {
  try {
    console.log('🔧 手动触发弹窗显示...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此测试');
      return;
    }
    
    // 检查公告数据
    const announcements = currentPage.data.announcements;
    if (!announcements || announcements.length === 0) {
      console.log('❌ 没有公告数据');
      return;
    }
    
    // 准备测试数据
    const testAnnouncement = announcements[0];
    const testData = {
      ...testAnnouncement,
      publishTimeFormatted: new Date(testAnnouncement.publishTime).toLocaleString('zh-CN'),
      expireTimeFormatted: testAnnouncement.expireTime ? new Date(testAnnouncement.expireTime).toLocaleString('zh-CN') : ''
    };
    
    console.log('📄 测试公告数据:', testData);
    
    // 查找公告组件并设置数据
    const query = wx.createSelectorQuery();
    query.select('announcement-banner').context();
    query.exec((res) => {
      if (res[0] && res[0].context) {
        const component = res[0].context;
        console.log('✅ 找到公告组件，设置弹窗数据...');
        
        component.setData({
          showModal: true,
          selectedAnnouncement: testData
        });
        
        console.log('✅ 弹窗数据已设置');
        
        // 检查设置结果
        setTimeout(() => {
          console.log('📊 设置后的组件数据:', component.data);
        }, 500);
        
      } else {
        console.log('❌ 没有找到公告组件');
      }
    });
    
  } catch (error) {
    console.error('❌ 手动触发弹窗异常:', error);
  }
}

// 测试关闭弹窗
function testCloseModal() {
  try {
    console.log('🔧 测试关闭弹窗...');
    
    const query = wx.createSelectorQuery();
    query.select('announcement-banner').context();
    query.exec((res) => {
      if (res[0] && res[0].context) {
        const component = res[0].context;
        console.log('✅ 找到公告组件，关闭弹窗...');
        
        // 调用关闭方法
        if (typeof component.onCloseModal === 'function') {
          component.onCloseModal();
          console.log('✅ 弹窗关闭方法已调用');
          
          // 检查关闭结果
          setTimeout(() => {
            console.log('📊 关闭后的组件数据:', component.data);
            console.log('📊 弹窗显示状态:', component.data.showModal);
          }, 500);
          
        } else {
          console.log('❌ 组件没有 onCloseModal 方法');
        }
        
      } else {
        console.log('❌ 没有找到公告组件');
      }
    });
    
  } catch (error) {
    console.error('❌ 测试关闭弹窗异常:', error);
  }
}

// 完整的弹窗测试流程
async function fullModalTest() {
  try {
    console.log('🧪 开始完整弹窗测试流程...');
    
    // 1. 检查弹窗元素
    console.log('步骤 1: 检查弹窗元素');
    checkModalElements();
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. 手动显示弹窗
    console.log('步骤 2: 手动显示弹窗');
    manualShowModal();
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 测试关闭弹窗
    console.log('步骤 3: 测试关闭弹窗');
    testCloseModal();
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 4. 测试点击触发
    console.log('步骤 4: 测试点击触发');
    testModalDisplay();
    
    console.log('🎉 完整弹窗测试流程完成！');
    
  } catch (error) {
    console.error('❌ 完整弹窗测试异常:', error);
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.testModalDisplay = testModalDisplay;
  window.checkModalElements = checkModalElements;
  window.manualShowModal = manualShowModal;
  window.testCloseModal = testCloseModal;
  window.fullModalTest = fullModalTest;
}

console.log('📋 公告弹窗测试脚本加载完成！');
console.log('💡 使用方法：');
console.log('  - 测试弹窗显示: testModalDisplay()');
console.log('  - 检查弹窗元素: checkModalElements()');
console.log('  - 手动显示弹窗: manualShowModal()');
console.log('  - 测试关闭弹窗: testCloseModal()');
console.log('  - 完整测试流程: fullModalTest()');

console.log('🔄 自动开始完整测试流程...');
fullModalTest();
