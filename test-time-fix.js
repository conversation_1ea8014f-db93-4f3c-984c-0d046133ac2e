// 测试订单时间显示"未知"问题修复效果的脚本
// 在微信开发者工具的控制台中运行此脚本

console.log('🧪 开始测试订单时间显示修复效果...');

// 1. 模拟各种可能的时间数据格式
const testTimeFormats = [
  { name: 'Date对象', value: new Date(), expected: 'valid' },
  { name: 'ISO字符串', value: '2025-07-31T14:33:35.000Z', expected: 'valid' },
  { name: '时间戳', value: Date.now(), expected: 'valid' },
  { name: '空对象（setData序列化结果）', value: {}, expected: 'unknown' },
  { name: 'null', value: null, expected: 'unknown' },
  { name: 'undefined', value: undefined, expected: 'unknown' },
  { name: '空字符串', value: '', expected: 'unknown' },
  { name: '无效字符串', value: 'invalid-date', expected: 'unknown' }
];

// 2. 模拟修复后的订单卡片formatTime方法
function testOrderCardFormatTime(dateStr) {
  console.log('🕐 [测试] formatTime输入:', {
    value: dateStr,
    type: typeof dateStr,
    isDate: dateStr instanceof Date,
    isEmptyObject: typeof dateStr === 'object' && dateStr !== null && Object.keys(dateStr).length === 0
  });

  // 检查空值或无效值
  if (!dateStr || dateStr === '' || dateStr === null || dateStr === undefined) {
    return '未知';
  }

  // 检查是否为空对象（setData序列化Date对象后的结果）
  if (typeof dateStr === 'object' && dateStr !== null && !(dateStr instanceof Date)) {
    if (Object.keys(dateStr).length === 0) {
      console.warn('🕐 [测试] 检测到空对象，可能是setData序列化Date对象的结果:', dateStr);
      return '未知';
    }
  }

  let date;
  
  // 处理不同类型的时间输入
  if (dateStr instanceof Date) {
    date = dateStr;
  } else if (typeof dateStr === 'string') {
    date = new Date(dateStr);
  } else if (typeof dateStr === 'number') {
    date = new Date(dateStr);
  } else {
    console.warn('🕐 [测试] 不支持的时间格式:', typeof dateStr, dateStr);
    return '未知';
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('🕐 [测试] Invalid date:', dateStr);
    return '未知';
  }

  const now = new Date();
  const diff = now - date;

  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
  
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${month}-${day}`;
}

// 3. 模拟修复后的ensureTimeStringFormat方法
function testEnsureTimeStringFormat(timeValue) {
  if (!timeValue) {
    return null;
  }
  
  if (timeValue instanceof Date) {
    return timeValue.toISOString();
  }
  
  if (typeof timeValue === 'string') {
    return timeValue;
  }
  
  if (typeof timeValue === 'number') {
    return new Date(timeValue).toISOString();
  }
  
  if (typeof timeValue === 'object' && Object.keys(timeValue).length === 0) {
    console.warn('🕐 [测试] 检测到空对象时间字段，可能是setData序列化问题:', timeValue);
    return null;
  }
  
  try {
    return new Date(timeValue).toISOString();
  } catch (error) {
    console.warn('🕐 [测试] 时间格式转换失败:', timeValue, error);
    return null;
  }
}

// 4. 测试各种时间格式
console.log('\n🧪 [测试] 各种时间格式处理:');
testTimeFormats.forEach(testCase => {
  console.log(`\n--- 测试: ${testCase.name} ---`);
  
  // 测试ensureTimeStringFormat
  const formatted = testEnsureTimeStringFormat(testCase.value);
  console.log('ensureTimeStringFormat结果:', formatted);
  
  // 测试订单卡片formatTime
  const displayTime = testOrderCardFormatTime(testCase.value);
  console.log('订单卡片显示时间:', displayTime);
  
  // 验证结果
  const isExpectedResult = testCase.expected === 'valid' ? displayTime !== '未知' : displayTime === '未知';
  console.log('测试结果:', isExpectedResult ? '✅ 通过' : '❌ 失败');
});

// 5. 模拟页面切换场景
console.log('\n🔄 [测试] 页面切换场景:');

// 模拟初始订单数据（包含Date对象）
const mockOrder = {
  _id: 'test_order_1',
  title: '测试订单',
  createTime: new Date(),
  updateTime: new Date(),
  status: 'pending'
};

console.log('原始订单数据:', mockOrder);

// 模拟formatOrderData处理
const formattedOrder = {
  ...mockOrder,
  createTime: testEnsureTimeStringFormat(mockOrder.createTime),
  updateTime: testEnsureTimeStringFormat(mockOrder.updateTime)
};

console.log('格式化后订单数据:', formattedOrder);

// 模拟setData序列化
const serialized = JSON.parse(JSON.stringify({ latestOrders: [formattedOrder] }));
console.log('setData序列化后:', serialized);

// 模拟页面切换后的数据验证和修复
const orderAfterSwitch = serialized.latestOrders[0];
const fixedCreateTime = testEnsureTimeStringFormat(orderAfterSwitch.createTime);
const displayTime = testOrderCardFormatTime(fixedCreateTime);

console.log('页面切换后时间字段:', orderAfterSwitch.createTime);
console.log('修复后时间字段:', fixedCreateTime);
console.log('最终显示时间:', displayTime);

// 6. 测试总结
console.log('\n📋 [测试总结]');
console.log('✅ 修复内容:');
console.log('   1. 订单卡片组件增强了对空对象的检测');
console.log('   2. 首页数据格式化统一使用ensureTimeStringFormat方法');
console.log('   3. 页面切换时增加了时间字段验证和修复机制');
console.log('   4. 所有setData操作前都确保时间字段为字符串格式');

console.log('\n💡 [建议测试步骤]:');
console.log('   1. 发布一个新订单');
console.log('   2. 确认首页订单卡片显示正确的时间（如"刚刚"）');
console.log('   3. 切换到"我的订单"页面');
console.log('   4. 再切换回首页');
console.log('   5. 重复步骤3-4多次');
console.log('   6. 确认订单时间始终正确显示，不会变成"未知"');

console.log('\n🔍 [调试信息]:');
console.log('   - 查看控制台中的"🕐 [订单卡片]"日志');
console.log('   - 查看控制台中的"🔧 [首页]"日志');
console.log('   - 注意时间字段的类型变化过程');
