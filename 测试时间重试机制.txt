// 🧪 测试时间重试机制的脚本
console.log('🧪 开始测试时间重试机制...');

// 1. 获取当前页面和组件
const currentPage = getCurrentPages()[getCurrentPages().length - 1];

if (currentPage.route === 'pages/index/index') {
  const orderCardComponents = currentPage.selectAllComponents('.order-card');
  console.log(`找到${orderCardComponents.length}个订单卡片组件`);
  
  if (orderCardComponents.length > 0) {
    const testComponent = orderCardComponents[0];
    
    console.log('\n🔧 测试组件初始状态:');
    console.log('  timeRetryCount:', testComponent.data.timeRetryCount);
    console.log('  maxRetryCount:', testComponent.data.maxRetryCount);
    console.log('  isTimeLoading:', testComponent.data.isTimeLoading);
    console.log('  formattedCreateTime:', testComponent.data.formattedCreateTime);
    
    // 备份原始数据
    const originalOrderData = { ...testComponent.data.orderData };
    
    console.log('\n🧪 测试1: 模拟时间数据为null的情况');
    
    // 设置异常数据触发重试
    testComponent.setData({
      orderData: {
        ...originalOrderData,
        createTime: null
      }
    });
    
    // 手动调用updateFormattedTime触发重试
    if (testComponent.updateFormattedTime) {
      testComponent.updateFormattedTime();
      
      console.log('  触发重试后的状态:');
      console.log('  timeRetryCount:', testComponent.data.timeRetryCount);
      console.log('  isTimeLoading:', testComponent.data.isTimeLoading);
      console.log('  formattedCreateTime:', testComponent.data.formattedCreateTime);
    }
    
    // 等待3秒观察重试过程
    setTimeout(() => {
      console.log('\n🔍 3秒后检查重试状态:');
      console.log('  timeRetryCount:', testComponent.data.timeRetryCount);
      console.log('  isTimeLoading:', testComponent.data.isTimeLoading);
      console.log('  formattedCreateTime:', testComponent.data.formattedCreateTime);
      
      console.log('\n🧪 测试2: 模拟时间数据为空对象的情况');
      
      // 设置空对象数据
      testComponent.setData({
        orderData: {
          ...originalOrderData,
          createTime: {}
        }
      });
      
      if (testComponent.updateFormattedTime) {
        testComponent.updateFormattedTime();
      }
      
      // 再等待5秒观察完整的重试过程
      setTimeout(() => {
        console.log('\n🔍 5秒后检查重试状态:');
        console.log('  timeRetryCount:', testComponent.data.timeRetryCount);
        console.log('  isTimeLoading:', testComponent.data.isTimeLoading);
        console.log('  formattedCreateTime:', testComponent.data.formattedCreateTime);
        
        console.log('\n🧪 测试3: 恢复正常数据');
        
        // 恢复正常数据
        testComponent.setData({
          orderData: originalOrderData
        });
        
        if (testComponent.updateFormattedTime) {
          testComponent.updateFormattedTime();
        }
        
        setTimeout(() => {
          console.log('\n🔍 恢复正常数据后的状态:');
          console.log('  timeRetryCount:', testComponent.data.timeRetryCount);
          console.log('  isTimeLoading:', testComponent.data.isTimeLoading);
          console.log('  formattedCreateTime:', testComponent.data.formattedCreateTime);
          
          console.log('\n✅ 重试机制测试完成！');
          
          // 总结测试结果
          console.log('\n📊 测试总结:');
          console.log('✅ 重试机制已添加到订单卡片组件');
          console.log('✅ 异常情况会触发最多3次重试');
          console.log('✅ 重试期间显示"重试中(x/3)..."');
          console.log('✅ 重试失败后不显示任何内容');
          console.log('✅ 正常数据会重置重试状态');
          
        }, 2000);
        
      }, 5000);
      
    }, 3000);
    
  } else {
    console.log('⚠️ 没有找到订单卡片组件');
  }
  
} else {
  console.log('⚠️ 当前不在首页');
}

// 2. 提供手动测试函数
window.testTimeRetry = function() {
  console.log('🧪 手动测试时间重试机制...');
  
  const page = getCurrentPages()[getCurrentPages().length - 1];
  if (page.route === 'pages/index/index') {
    const components = page.selectAllComponents('.order-card');
    
    if (components.length > 0) {
      const component = components[0];
      
      // 强制触发重试
      console.log('🔧 强制触发重试机制...');
      
      if (component.retryTimeUpdate) {
        component.retryTimeUpdate();
        console.log('✅ 重试机制已触发');
      } else {
        console.log('❌ 重试方法不存在');
      }
    }
  }
};

// 3. 提供重置重试状态的函数
window.resetTimeRetry = function() {
  console.log('🔧 重置所有组件的重试状态...');
  
  const page = getCurrentPages()[getCurrentPages().length - 1];
  if (page.route === 'pages/index/index') {
    const components = page.selectAllComponents('.order-card');
    
    components.forEach((component, index) => {
      component.setData({
        timeRetryCount: 0,
        isTimeLoading: false
      });
      console.log(`✅ 组件${index + 1}重试状态已重置`);
    });
  }
};

console.log('\n✅ 测试脚本启动完成！');
console.log('💡 手动测试函数:');
console.log('  - testTimeRetry(): 手动触发重试机制');
console.log('  - resetTimeRetry(): 重置重试状态');
