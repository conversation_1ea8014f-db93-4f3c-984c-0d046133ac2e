// 公告详情页面
Page({
  data: {
    announcement: null,
    relatedAnnouncements: [],
    loading: true,
    error: ''
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      this.setData({
        loading: false,
        error: '公告ID不能为空'
      });
      return;
    }

    this.announcementId = id;
    this.loadAnnouncementDetail();
  },

  // 加载公告详情
  async loadAnnouncementDetail() {
    try {
      this.setData({
        loading: true,
        error: ''
      });

      console.log('📋 [公告详情] 开始加载，ID:', this.announcementId);

      // 获取公告详情
      const result = await wx.cloud.callFunction({
        name: 'announcementManager',
        data: {
          action: 'getDetail',
          announcementId: this.announcementId
        }
      });

      if (result.result && result.result.success) {
        const announcement = result.result.data;
        console.log('📋 [公告详情] 加载成功:', announcement.title);

        this.setData({
          announcement: announcement,
          loading: false
        });

        // 加载相关公告
        this.loadRelatedAnnouncements();

      } else {
        console.error('📋 [公告详情] 加载失败:', result.result?.error);
        this.setData({
          loading: false,
          error: result.result?.error || '加载公告详情失败'
        });
      }

    } catch (error) {
      console.error('📋 [公告详情] 加载异常:', error);
      this.setData({
        loading: false,
        error: '网络错误，请稍后重试'
      });
    }
  },

  // 加载相关公告
  async loadRelatedAnnouncements() {
    try {
      console.log('📋 [相关公告] 开始加载');

      const result = await wx.cloud.callFunction({
        name: 'getAnnouncementList',
        data: {
          page: 1,
          pageSize: 5,
          status: 'active',
          isAdmin: false
        }
      });

      if (result.result && result.result.success) {
        const allAnnouncements = result.result.data.list || [];
        
        // 过滤掉当前公告
        const relatedAnnouncements = allAnnouncements.filter(
          item => item._id !== this.announcementId
        ).slice(0, 3); // 只显示3条相关公告

        console.log('📋 [相关公告] 加载成功，数量:', relatedAnnouncements.length);

        this.setData({
          relatedAnnouncements: relatedAnnouncements
        });
      }

    } catch (error) {
      console.error('📋 [相关公告] 加载异常:', error);
      // 相关公告加载失败不影响主要功能，只记录错误
    }
  },

  // 点击相关公告
  onRelatedAnnouncementTap(e) {
    const { id } = e.currentTarget.dataset;
    if (!id) return;

    console.log('📋 [相关公告] 点击跳转，ID:', id);

    // 跳转到新的公告详情页
    wx.redirectTo({
      url: `/pages/announcement/detail/detail?id=${id}`
    });
  },

  // 分享功能
  onShareAppMessage() {
    const { announcement } = this.data;
    
    return {
      title: announcement ? announcement.title : '查看公告详情',
      path: `/pages/announcement/detail/detail?id=${this.announcementId}`,
      imageUrl: '' // 可以设置分享图片
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { announcement } = this.data;
    
    return {
      title: announcement ? announcement.title : '查看公告详情',
      query: `id=${this.announcementId}`,
      imageUrl: '' // 可以设置分享图片
    };
  }
});
