// 修复用户数据云函数
const cloud = require('wx-server-sdk');
const TimeZoneUtils = require('../common/timeZoneUtils');

cloud.init({
  env: 'cloud1-9gsj7t48183e5a9f'
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔧 [修复用户数据] 开始修复用户数据');
  console.log('🔧 [修复用户数据] 接收参数:', event);

  const { action } = event;

  try {
    switch (action) {
      case 'initializeLastActiveTime':
        return await initializeLastActiveTime();
      case 'updateActiveTime':
        return await updateActiveTime();
      case 'batchInitializeLastActiveTime':
        return await batchInitializeLastActiveTime();
      default:
        return {
          success: false,
          error: '未知的修复操作'
        };
    }
  } catch (error) {
    console.error('❌ [修复用户数据] 修复失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 为缺少 lastActiveTime 字段的用户初始化
async function initializeLastActiveTime() {
  try {
    console.log('🔧 [初始化活跃时间] 开始为缺少字段的用户初始化 lastActiveTime');

    // 获取所有用户
    const usersResult = await db.collection('users').get();
    const users = usersResult.data;

    let updatedCount = 0;
    const now = TimeZoneUtils.createStorageTime();

    for (const user of users) {
      if (!user.lastActiveTime) {
        // 为没有 lastActiveTime 字段的用户添加该字段
        // 使用用户的 updateTime 或 createTime 作为初始值
        const initialTime = user.updateTime || user.createTime || now;
        
        await db.collection('users').doc(user._id).update({
          data: {
            lastActiveTime: initialTime,
            updateTime: now
          }
        });

        updatedCount++;
        console.log(`✅ [初始化活跃时间] 已为用户 ${user.nickName || user._id} 初始化 lastActiveTime`);
      }
    }

    console.log(`🎉 [初始化活跃时间] 完成，共更新了 ${updatedCount} 个用户`);

    return {
      success: true,
      message: `成功为 ${updatedCount} 个用户初始化 lastActiveTime 字段`,
      data: {
        totalUsers: users.length,
        updatedUsers: updatedCount
      }
    };

  } catch (error) {
    console.error('❌ [初始化活跃时间] 初始化失败:', error);
    throw error;
  }
}

// 更新所有用户的活跃时间为当前时间
async function updateActiveTime() {
  try {
    console.log('🔧 [更新活跃时间] 开始更新所有用户的活跃时间为当前时间');

    // 获取所有用户
    const usersResult = await db.collection('users').get();
    const users = usersResult.data;

    let updatedCount = 0;
    const now = TimeZoneUtils.createStorageTime();

    for (const user of users) {
      await db.collection('users').doc(user._id).update({
        data: {
          lastActiveTime: now,
          updateTime: now
        }
      });

      updatedCount++;
      console.log(`✅ [更新活跃时间] 已更新用户 ${user.nickName || user._id} 的活跃时间`);
    }

    console.log(`🎉 [更新活跃时间] 完成，共更新了 ${updatedCount} 个用户`);

    return {
      success: true,
      message: `成功更新了 ${updatedCount} 个用户的活跃时间`,
      data: {
        totalUsers: users.length,
        updatedUsers: updatedCount,
        updateTime: now.toISOString()
      }
    };

  } catch (error) {
    console.error('❌ [更新活跃时间] 更新失败:', error);
    throw error;
  }
}

// 批量初始化所有用户的 lastActiveTime 字段
async function batchInitializeLastActiveTime() {
  try {
    console.log('🔧 [批量初始化] 开始批量初始化所有用户的 lastActiveTime 字段');

    // 获取所有用户
    const usersResult = await db.collection('users').get();
    const users = usersResult.data;

    let updatedCount = 0;
    const now = TimeZoneUtils.createStorageTime();

    for (const user of users) {
      // 为所有用户设置 lastActiveTime 字段
      // 如果用户已经有该字段，则保持不变；如果没有，则使用当前时间
      const updateData = {
        updateTime: now
      };

      if (!user.lastActiveTime) {
        updateData.lastActiveTime = user.updateTime || user.createTime || now;
      }

      await db.collection('users').doc(user._id).update({
        data: updateData
      });

      updatedCount++;
      console.log(`✅ [批量初始化] 已处理用户 ${user.nickName || user._id}`);
    }

    console.log(`🎉 [批量初始化] 完成，共处理了 ${updatedCount} 个用户`);

    return {
      success: true,
      message: `成功处理了 ${updatedCount} 个用户的 lastActiveTime 字段`,
      data: {
        totalUsers: users.length,
        processedUsers: updatedCount
      }
    };

  } catch (error) {
    console.error('❌ [批量初始化] 批量初始化失败:', error);
    throw error;
  }
}
