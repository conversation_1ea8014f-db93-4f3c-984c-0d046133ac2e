// 创建测试公告的脚本
// 在微信开发者工具控制台中运行此脚本

console.log('🚀 开始创建测试公告...');

// 测试公告数据
const testAnnouncement = {
  title: '🎉 欢迎使用三角洲接单平台！',
  content: '亲爱的用户，欢迎来到三角洲接单平台！我们致力于为您提供最优质的游戏代练服务。平台功能包括：\n\n✨ 智能任务匹配系统\n🛡️ 安全保障机制\n💰 便捷的钱包系统\n📞 24小时客服支持\n\n如有任何问题，请随时联系我们的客服团队。祝您使用愉快！',
  type: 'notice',
  priority: 1,
  status: 'active',
  publishTime: new Date(),
  effectiveTime: new Date(),
  expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
  isTop: true
};

// 创建测试公告的函数
async function createTestAnnouncement() {
  try {
    console.log('📝 正在创建测试公告...');
    
    // 首先初始化数据库（如果还没有初始化）
    console.log('🔧 初始化公告数据库...');
    const initResult = await wx.cloud.callFunction({
      name: 'initAnnouncementDatabase'
    });
    
    if (initResult.result && initResult.result.success) {
      console.log('✅ 数据库初始化成功');
    } else {
      console.log('⚠️ 数据库可能已经初始化过了');
    }
    
    // 创建测试公告
    const result = await wx.cloud.callFunction({
      name: 'announcementManager',
      data: {
        action: 'create',
        ...testAnnouncement
      }
    });
    
    if (result.result && result.result.success) {
      console.log('🎉 测试公告创建成功！');
      console.log('📄 公告ID:', result.result.data._id);
      console.log('📝 公告标题:', testAnnouncement.title);
      console.log('📅 发布时间:', testAnnouncement.publishTime);
      console.log('⏰ 过期时间:', testAnnouncement.expireTime);
      
      // 验证公告是否可以正常获取
      console.log('🔍 验证公告列表...');
      const listResult = await wx.cloud.callFunction({
        name: 'getAnnouncementList',
        data: {
          page: 1,
          pageSize: 10,
          status: 'active',
          isAdmin: false
        }
      });
      
      if (listResult.result && listResult.result.success) {
        const announcements = listResult.result.data.list;
        console.log('✅ 公告列表获取成功，共', announcements.length, '条公告');
        
        // 查找刚创建的公告
        const createdAnnouncement = announcements.find(item => item._id === result.result.data._id);
        if (createdAnnouncement) {
          console.log('✅ 测试公告在列表中找到了！');
          console.log('📊 公告信息:', {
            id: createdAnnouncement._id,
            title: createdAnnouncement.title,
            type: createdAnnouncement.type,
            status: createdAnnouncement.status,
            isTop: createdAnnouncement.isTop
          });
        } else {
          console.log('⚠️ 测试公告在列表中未找到，可能需要刷新');
        }
      } else {
        console.error('❌ 公告列表获取失败:', listResult.result?.error);
      }
      
    } else {
      console.error('❌ 测试公告创建失败:', result.result?.error);
    }
    
  } catch (error) {
    console.error('❌ 创建测试公告时发生异常:', error);
  }
}

// 创建多个测试公告的函数
async function createMultipleTestAnnouncements() {
  const announcements = [
    {
      title: '🎉 欢迎使用三角洲接单平台！',
      content: '亲爱的用户，欢迎来到三角洲接单平台！我们致力于为您提供最优质的游戏代练服务。',
      type: 'notice',
      priority: 1,
      status: 'active',
      isTop: true
    },
    {
      title: '🔧 系统维护通知',
      content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用，请提前做好准备。',
      type: 'system',
      priority: 1,
      status: 'active',
      isTop: true
    },
    {
      title: '🚨 安全提醒',
      content: '请注意保护个人信息安全，不要向陌生人透露账号密码。如遇可疑情况，请及时联系客服。',
      type: 'urgent',
      priority: 1,
      status: 'active',
      isTop: false
    },
    {
      title: '🆕 新功能上线',
      content: '全新的任务匹配系统已经上线！现在您可以更快速地找到合适的任务伙伴，提升游戏体验。',
      type: 'notice',
      priority: 2,
      status: 'active',
      isTop: false
    }
  ];
  
  console.log('🚀 开始创建多个测试公告...');
  
  for (let i = 0; i < announcements.length; i++) {
    const announcement = announcements[i];
    try {
      const result = await wx.cloud.callFunction({
        name: 'announcementManager',
        data: {
          action: 'create',
          ...announcement,
          publishTime: new Date(),
          effectiveTime: new Date(),
          expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        }
      });
      
      if (result.result && result.result.success) {
        console.log(`✅ 公告 ${i + 1} 创建成功: ${announcement.title}`);
      } else {
        console.error(`❌ 公告 ${i + 1} 创建失败:`, result.result?.error);
      }
      
      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.error(`❌ 公告 ${i + 1} 创建异常:`, error);
    }
  }
  
  console.log('🎉 所有测试公告创建完成！');
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.createTestAnnouncement = createTestAnnouncement;
  window.createMultipleTestAnnouncements = createMultipleTestAnnouncements;
}

console.log('📋 测试公告创建脚本加载完成！');
console.log('💡 使用方法：');
console.log('  - 创建单个测试公告: createTestAnnouncement()');
console.log('  - 创建多个测试公告: createMultipleTestAnnouncements()');

// 自动执行创建测试公告
console.log('🔄 自动创建测试公告...');
createTestAnnouncement();
