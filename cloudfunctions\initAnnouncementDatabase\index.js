// 初始化公告数据库云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  try {
    console.log('开始初始化公告数据库...');

    // 创建公告集合
    const announcementCollection = db.collection('announcements');

    // 检查集合是否已存在
    try {
      await announcementCollection.limit(1).get();
      console.log('公告集合已存在');
    } catch (error) {
      if (error.errCode === -502005 || error.errCode === -502002) {
        console.log('公告集合不存在，将在第一次插入数据时自动创建');
      }
    }

    // 创建示例公告数据
    const sampleAnnouncements = [
      {
        title: '🎉 三角洲接单平台正式上线！',
        content: '欢迎使用三角洲接单平台！我们致力于为用户提供安全、高效的接单服务。平台支持多种任务类型，快来体验吧！',
        type: 'system', // system: 系统公告, notice: 普通通知, urgent: 紧急通知
        priority: 1, // 优先级：1-高，2-中，3-低
        status: 'active', // active: 激活, inactive: 停用, expired: 已过期
        publishTime: new Date(),
        effectiveTime: new Date(),
        expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
        isTop: true, // 是否置顶
        viewCount: 0, // 查看次数
        createdBy: 'system',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        title: '📢 平台维护通知',
        content: '为了提供更好的服务体验，平台将于每周日凌晨2:00-4:00进行系统维护，期间可能影响部分功能使用，请合理安排时间。',
        type: 'notice',
        priority: 2,
        status: 'active',
        publishTime: new Date(),
        effectiveTime: new Date(),
        expireTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
        isTop: false,
        viewCount: 0,
        createdBy: 'admin',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        title: '⚡ 新功能上线预告',
        content: '即将上线语音通话功能，让沟通更加便捷！敬请期待。',
        type: 'notice',
        priority: 3,
        status: 'active',
        publishTime: new Date(),
        effectiveTime: new Date(),
        expireTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14天后过期
        isTop: false,
        viewCount: 0,
        createdBy: 'admin',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // 插入示例数据
    for (const announcement of sampleAnnouncements) {
      try {
        const result = await announcementCollection.add({
          data: announcement
        });
        console.log(`插入公告成功: ${announcement.title}, ID: ${result._id}`);
      } catch (insertError) {
        console.error(`插入公告失败: ${announcement.title}`, insertError);
      }
    }

    // 创建索引（提高查询性能）
    try {
      // 为常用查询字段创建索引
      await db.collection('announcements').createIndex({
        keys: {
          status: 1,
          publishTime: -1
        },
        name: 'status_publishTime_index'
      });

      await db.collection('announcements').createIndex({
        keys: {
          isTop: -1,
          priority: 1,
          publishTime: -1
        },
        name: 'display_order_index'
      });

      console.log('索引创建成功');
    } catch (indexError) {
      console.log('索引创建失败或已存在:', indexError.message);
    }

    return {
      success: true,
      message: '公告数据库初始化成功',
      sampleCount: sampleAnnouncements.length
    };

  } catch (error) {
    console.error('初始化公告数据库失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
