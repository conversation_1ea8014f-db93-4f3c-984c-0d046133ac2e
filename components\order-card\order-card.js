// 统一订单卡片组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 订单数据
    orderData: {
      type: Object,
      value: {}
    },
    // 是否显示详细信息
    showDetail: {
      type: Boolean,
      value: true
    },
    // 卡片模式：'normal' | 'compact'
    mode: {
      type: String,
      value: 'normal'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    displayTitle: '',
    formattedCreateTime: '', // 🔧 初始为空字符串，避免显示"未知"
    timeUpdateTimer: null, // 时间更新定时器
    isDataReady: false, // 🔧 标记数据是否就绪
    timeRetryCount: 0, // 🔧 时间重试次数
    maxRetryCount: 3, // 🔧 最大重试次数
    isTimeLoading: false // 🔧 时间是否正在加载
  },

  /**
   * 组件的观察器
   */
  observers: {
    'orderData': function(orderData) {
      console.log('🕐 [订单卡片] orderData观察器触发:', {
        hasOrderData: !!orderData,
        hasId: !!(orderData && orderData._id),
        hasCreateTime: !!(orderData && orderData.createTime)
      });

      // 🔧 修复时序问题：确保有完整的订单数据才更新
      if (orderData && orderData._id) {
        console.log('🕐 [订单卡片] 订单数据就绪，开始更新显示');

        // 标记数据已就绪，重置重试状态
        this.setData({
          isDataReady: true,
          timeRetryCount: 0,
          isTimeLoading: false
        });

        this.updateDisplayTitle();
        this.updateFormattedTime();

        // 如果定时器还没启动，现在启动
        if (!this.data.timeUpdateTimer) {
          this.startTimeUpdateTimer();
        }
      } else if (orderData === null || orderData === undefined) {
        // 数据被清空，重置显示
        console.log('🕐 [订单卡片] 订单数据被清空');
        this.setData({
          formattedCreateTime: '',
          displayTitle: '',
          isDataReady: false
        });
        this.stopTimeUpdateTimer();
      } else {
        // 数据不完整，保持等待状态
        console.log('🕐 [订单卡片] 订单数据不完整，保持等待状态');
        this.setData({
          formattedCreateTime: '',
          displayTitle: '',
          isDataReady: false
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 更新显示标题
    updateDisplayTitle() {
      const orderData = this.data.orderData;
      if (!orderData) {
        this.setData({ displayTitle: '' });
        return;
      }

      const originalTitle = orderData.title || orderData.gameInfo?.gameName || '5元战神';
      const maxLength = 10;

      let displayTitle = originalTitle;
      if (originalTitle.length > maxLength) {
        displayTitle = originalTitle.substring(0, maxLength) + '...';
      }

      this.setData({ displayTitle });
    },

    // 更新格式化时间
    updateFormattedTime() {
      const orderData = this.data.orderData;

      // 🔧 修复时序问题：更严格的数据验证
      if (!orderData || !orderData._id) {
        console.log('🕐 [订单卡片] 订单数据不完整，跳过时间更新:', {
          hasOrderData: !!orderData,
          hasId: !!(orderData && orderData._id)
        });
        // 不设置"未知"，保持空白状态等待数据
        return;
      }

      // 检查createTime字段是否存在且有效
      const createTime = orderData.createTime;

      // 🔧 修复：异常情况启动重试机制
      if (!createTime || createTime === null || createTime === undefined || createTime === '') {
        console.log('🕐 [订单卡片] 时间数据无效，启动重试机制:', {
          createTime,
          type: typeof createTime,
          retryCount: this.data.timeRetryCount,
          maxRetry: this.data.maxRetryCount
        });

        // 启动重试机制
        this.retryTimeUpdate();
        return;
      }

      // 检查是否为空对象（setData序列化Date对象的结果）
      if (typeof createTime === 'object' && createTime !== null && !(createTime instanceof Date)) {
        if (Object.keys(createTime).length === 0) {
          console.warn('🕐 [订单卡片] 检测到空对象时间字段，启动重试机制:', createTime);
          this.retryTimeUpdate();
          return;
        }
      }

      console.log('🕐 [订单卡片] 格式化时间:', {
        createTime: createTime,
        type: typeof createTime,
        isDate: createTime instanceof Date,
        isEmptyObject: typeof createTime === 'object' && createTime !== null && Object.keys(createTime).length === 0
      });

      const formattedTime = this.formatTime(createTime);
      console.log('🕐 [订单卡片] 格式化结果:', formattedTime);

      // 🔧 最终安全检查：确保永远不设置"未知"相关的文本
      const safeFormattedTime = (formattedTime === '未知' || formattedTime === '时间未知') ? '' : formattedTime;
      if (safeFormattedTime !== formattedTime) {
        console.warn('🔧 [订单卡片] 检测到"未知"显示，修正为空字符串:', { original: formattedTime, corrected: safeFormattedTime });
      }

      // 🔧 成功格式化时间后，重置重试状态
      this.setData({
        formattedCreateTime: safeFormattedTime,
        timeRetryCount: 0,
        isTimeLoading: false
      });
    },

    // 格式化时间
    formatTime(dateStr) {
      console.log('🕐 [订单卡片] formatTime输入详情:', {
        value: dateStr,
        type: typeof dateStr,
        isDate: dateStr instanceof Date,
        isNull: dateStr === null,
        isUndefined: dateStr === undefined,
        isEmpty: dateStr === '',
        isEmptyObject: typeof dateStr === 'object' && dateStr !== null && Object.keys(dateStr).length === 0,
        stringValue: String(dateStr)
      });

      // 检查空值或无效值
      // 🔧 修复：异常情况返回空字符串，不显示任何内容
      if (!dateStr || dateStr === '' || dateStr === null || dateStr === undefined) {
        console.log('🕐 [订单卡片] 空值或无效值，返回空字符串');
        return ''; // 空值时不显示任何内容
      }

      // 检查是否为空对象（setData序列化Date对象后的结果）
      if (typeof dateStr === 'object' && dateStr !== null && !(dateStr instanceof Date)) {
        if (Object.keys(dateStr).length === 0) {
          console.warn('🕐 [订单卡片] 检测到空对象，返回空字符串:', dateStr);
          return ''; // 空对象时不显示任何内容
        }
      }

      let date;

      // 处理不同类型的时间输入
      if (dateStr instanceof Date) {
        date = dateStr;
      } else if (typeof dateStr === 'string') {
        // 尝试解析字符串格式的时间
        date = new Date(dateStr);
      } else if (typeof dateStr === 'number') {
        // 处理时间戳
        date = new Date(dateStr);
      } else {
        console.warn('🕐 [订单卡片] 不支持的时间格式:', typeof dateStr, dateStr, '返回空字符串');
        return ''; // 不支持的格式时不显示任何内容
      }

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('🕐 [订单卡片] Invalid date:', dateStr, '转换后的date:', date, '返回空字符串');
        return ''; // 无效日期时不显示任何内容
      }

      // 🔧 修复时区一致性问题：
      // 存储的时间是UTC时间，需要转换为中国时区时间进行显示
      // 获取当前中国时间用于计算时间差
      const now = new Date();
      const chinaOffset = 8 * 60; // 中国时区偏移量（分钟）
      const chinaTime = new Date(now.getTime() + chinaOffset * 60 * 1000);

      // 如果传入的是ISO字符串（UTC时间），需要转换为中国时区时间
      let displayDate = date;
      if (typeof dateStr === 'string' && dateStr.includes('T') && dateStr.includes('Z')) {
        // 这是UTC时间字符串，转换为中国时区时间进行显示
        displayDate = new Date(date.getTime() + chinaOffset * 60 * 1000);
        console.log('🕐 [订单卡片] UTC时间转换为中国时区:', {
          原始UTC: dateStr,
          UTC时间: date.toISOString(),
          中国时间: displayDate.toISOString(),
          当前中国时间: chinaTime.toISOString()
        });
      }

      const diff = chinaTime - displayDate;

      console.log('🕐 [订单卡片] 时间差计算:', {
        当前中国时间: chinaTime.toISOString(),
        订单时间: displayDate.toISOString(),
        时间差毫秒: diff,
        时间差分钟: Math.floor(diff / 60000)
      });

      // 处理负时间差（未来时间）
      if (diff < 0) {
        console.warn('🕐 [订单卡片] 检测到未来时间，可能存在时区问题:', {
          diff: diff,
          订单时间: displayDate.toISOString(),
          当前时间: chinaTime.toISOString()
        });
        return '刚刚'; // 对于未来时间，显示为"刚刚"
      }

      if (diff < 60000) { // 1分钟内
        return '刚刚';
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`;
      } else if (diff < 86400000) { // 1天内
        return `${Math.floor(diff / 3600000)}小时前`;
      } else {
        // 使用中国时区的日期进行格式化
        const month = String(displayDate.getMonth() + 1).padStart(2, '0');
        const day = String(displayDate.getDate()).padStart(2, '0');
        return `${month}-${day}`;
      }
    },

    // 启动时间更新定时器
    startTimeUpdateTimer() {
      // 清除现有定时器
      this.stopTimeUpdateTimer();

      // 每30秒更新一次时间显示
      const timer = setInterval(() => {
        this.updateFormattedTime();
      }, 30000); // 30秒

      this.setData({
        timeUpdateTimer: timer
      });

      console.log('🕐 [订单卡片] 启动时间更新定时器');
    },

    // 停止时间更新定时器
    stopTimeUpdateTimer() {
      if (this.data.timeUpdateTimer) {
        clearInterval(this.data.timeUpdateTimer);
        this.setData({
          timeUpdateTimer: null
        });
        console.log('🕐 [订单卡片] 停止时间更新定时器');
      }
    },

    // 卡片点击事件
    onCardTap(e) {
      const { id } = e.currentTarget.dataset;
      this.triggerEvent('cardtap', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 编辑订单
    onEditOrder(e) {
      console.log('onEditOrder 被调用', e);

      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }

      // 安全地获取数据
      const id = e && e.currentTarget && e.currentTarget.dataset ? e.currentTarget.dataset.id : null;
      if (!id) {
        console.error('无法获取订单ID');
        return;
      }



      this.triggerEvent('editorder', {
        orderId: id,
        orderData: this.data.orderData
      });
    },



    // 取消订单
    onCancelOrder(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;

      // 直接触发事件到父页面，让父页面处理取消原因选择
      this.triggerEvent('cancelorder', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 查看详情
    onViewDetail(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;
      console.log('🔍 [订单卡片] 查看详情点击:', {
        datasetId: id,
        orderData_id: this.data.orderData?._id,
        orderData_orderNo: this.data.orderData?.orderNo,
        orderData_orderId: this.data.orderData?.orderId,
        fullOrderData: this.data.orderData
      });
      this.triggerEvent('viewdetail', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 抢单
    onGrabOrder(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;
      const order = this.data.orderData;



      // 构建服务时间显示文本
      let serviceText = '';
      if (order.serviceType === 'rounds' || (order.rounds && !order.serviceType)) {
        serviceText = `局数：${order.rounds || 5}局`;
      } else {
        serviceText = `时长：${order.duration || 2}小时`;
      }

      wx.showModal({
        title: '确认抢单',
        content: `确定要抢这个战术任务吗？\n金额：¥${order.totalAmount || order.reward || 0}\n${serviceText}`,
        confirmText: '确认抢单',
        cancelText: '取消',
        confirmColor: '#4ab37e',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('graborder', {
              orderId: id,
              orderData: order
            });
          }
        }
      });
    },

    // 联系接单者
    onContactAccepter(e) {
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;

      this.triggerEvent('contactaccepter', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 进入聊天
    onEnterChat(e) {
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;

      this.triggerEvent('enterchat', {
        orderId: id,
        orderData: this.data.orderData
      });
    },

    // 评价订单
    onEvaluateOrder(e) {
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      const { id } = e.currentTarget.dataset;

      this.triggerEvent('evaluateorder', {
        orderId: id,
        orderData: this.data.orderData
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树时执行
      console.log('order-card attached', this.data.orderData);

      // 🔧 修复时序问题：只有在有有效订单数据时才初始化
      if (this.data.orderData && this.data.orderData._id) {
        console.log('🕐 [订单卡片] attached时订单数据已就绪');
        this.setData({ isDataReady: true });
        this.updateDisplayTitle();
        this.updateFormattedTime();
        // 启动时间更新定时器
        this.startTimeUpdateTimer();
      } else {
        console.log('🕐 [订单卡片] attached时订单数据未就绪，等待数据传入');
        // 设置初始状态，保持空白等待数据
        this.setData({
          formattedCreateTime: '',
          displayTitle: '',
          isDataReady: false
        });
      }
    },

    detached() {
      // 组件实例被从页面节点树移除时执行
      console.log('order-card detached');
      // 停止时间更新定时器
      this.stopTimeUpdateTimer();
    },

    // 🔧 新增：时间更新重试机制
    retryTimeUpdate() {
      const currentRetryCount = this.data.timeRetryCount;
      const maxRetryCount = this.data.maxRetryCount;

      if (currentRetryCount >= maxRetryCount) {
        console.warn('🕐 [订单卡片] 时间重试次数已达上限，停止重试:', {
          retryCount: currentRetryCount,
          maxRetry: maxRetryCount
        });

        // 重试失败，设置为空字符串（不显示）
        this.setData({
          formattedCreateTime: '',
          isTimeLoading: false,
          timeRetryCount: 0 // 重置重试计数
        });
        return;
      }

      console.log('🔄 [订单卡片] 启动时间重试:', {
        currentRetry: currentRetryCount + 1,
        maxRetry: maxRetryCount
      });

      // 增加重试计数
      this.setData({
        timeRetryCount: currentRetryCount + 1,
        isTimeLoading: true
      });

      // 延迟重试（递增延迟：1秒、2秒、3秒）
      const retryDelay = (currentRetryCount + 1) * 1000;

      setTimeout(() => {
        console.log('🔄 [订单卡片] 执行时间重试:', this.data.timeRetryCount);

        // 重新尝试更新时间
        const orderData = this.data.orderData;
        if (orderData && orderData.createTime) {
          try {
            const formattedTime = this.formatTime(orderData.createTime);

            if (formattedTime && formattedTime !== '' && formattedTime !== '未知' && formattedTime !== '时间未知') {
              // 重试成功
              console.log('✅ [订单卡片] 时间重试成功:', formattedTime);
              this.setData({
                formattedCreateTime: formattedTime,
                isTimeLoading: false,
                timeRetryCount: 0 // 重置重试计数
              });
            } else {
              // 重试仍然失败，继续下一次重试
              console.warn('🔄 [订单卡片] 时间重试仍然失败，准备下一次重试');
              this.retryTimeUpdate();
            }
          } catch (error) {
            console.error('❌ [订单卡片] 时间重试过程中出错:', error);
            this.retryTimeUpdate();
          }
        } else {
          // 数据仍然无效，继续重试
          console.warn('🔄 [订单卡片] 订单数据仍然无效，继续重试');
          this.retryTimeUpdate();
        }
      }, retryDelay);
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面被展示时启动定时器
      this.startTimeUpdateTimer();
    },

    hide() {
      // 页面被隐藏时停止定时器
      this.stopTimeUpdateTimer();
    }
  }
});
