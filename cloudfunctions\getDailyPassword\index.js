// 获取每日密码云函数 - 从数据库读取缓存
const cloud = require('wx-server-sdk');
const TimeZoneUtils = require('./common/timeZoneUtils');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();

  try {
    console.log('🔐 [获取每日密码] 从数据库获取缓存密码');

    // 获取今天的日期（使用中国时区）
    const todayStr = TimeZoneUtils.getChinaDateString();
    const todayStartUTC = TimeZoneUtils.getTodayStartUTC();

    console.log('🔐 [获取每日密码] 中国时区今日日期:', todayStr);
    console.log('🔐 [获取每日密码] 今日开始时间(UTC):', todayStartUTC.toISOString());

    // 从数据库查询今天的密码记录
    const passwordRecord = await db.collection('daily_passwords')
      .where({
        _createTime: db.command.gte(todayStartUTC)
      })
      .orderBy('_createTime', 'desc')
      .limit(1)
      .get();

    if (passwordRecord.data.length > 0) {
      const record = passwordRecord.data[0];
      console.log('🔐 [获取每日密码] 找到缓存记录:', record.date);

      const result = {
        success: record.success,
        data: {
          date: record.date,
          locations: record.locations,
          foundCount: record.foundCount,
          totalCount: record.totalCount,
          updateTime: record.updateTime,
          source: 'database_cache'
        },
        message: record.success ?
          `从缓存获取${record.foundCount}/${record.totalCount}个地点密码` :
          '缓存中的密码破译失败'
      };

      console.log('🔐 [获取每日密码] 从缓存获取完成:', result);
      return result;

    } else {
      // 如果没有找到今天的记录，返回默认失败状态
      console.log('🔐 [获取每日密码] 未找到今天的密码缓存');
      throw new Error('未找到今天的密码缓存记录');
    }
    
  } catch (error) {
    console.error('🔐 [获取每日密码] 从缓存获取失败:', error);

    // 返回破译失败的数据
    const now = new Date();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const defaultDate = `${month}月${day}日`;

    const failedPasswords = [
      { name: '零号大坝', code: '----' },
      { name: '长弓溪谷', code: '----' },
      { name: '巴克什', code: '----' },
      { name: '航天基地', code: '----' },
      { name: '潮汐监狱', code: '----' }
    ];

    return {
      success: false,
      data: {
        date: defaultDate,
        locations: failedPasswords,
        foundCount: 0,
        totalCount: 5,
        updateTime: new Date().toISOString(),
        source: 'fallback'
      },
      message: '密码系统离线',
      error: error.message
    };
  }
};
