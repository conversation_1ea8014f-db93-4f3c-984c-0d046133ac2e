// 添加测试公告的简单脚本
// 请在微信开发者工具的控制台中运行此代码

console.log('🚀 开始添加测试公告...');

// 测试公告数据
const testAnnouncements = [
  {
    title: '🎉 欢迎使用三角洲接单平台！',
    content: '亲爱的用户，欢迎来到三角洲接单平台！我们致力于为您提供最优质的游戏代练服务。\n\n✨ 平台特色功能：\n• 智能任务匹配系统\n• 安全保障机制\n• 便捷的钱包系统\n• 24小时客服支持\n\n如有任何问题，请随时联系我们的客服团队。祝您使用愉快！',
    type: 'notice',
    priority: 1,
    status: 'active',
    isTop: true
  },
  {
    title: '🔧 系统维护通知',
    content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用，请提前做好准备。维护完成后将为大家带来更好的使用体验！',
    type: 'system',
    priority: 1,
    status: 'active',
    isTop: true
  },
  {
    title: '🚨 安全提醒',
    content: '近期发现有不法分子冒充平台客服进行诈骗，请大家提高警惕！\n\n⚠️ 重要提醒：\n• 平台客服不会主动要求您提供密码\n• 不会要求您进行转账操作\n• 如有疑问请通过官方渠道联系我们\n\n保护好您的账户安全！',
    type: 'urgent',
    priority: 1,
    status: 'active',
    isTop: false
  }
];

// 添加测试公告的函数
async function addTestAnnouncements() {
  try {
    console.log('📊 初始化公告数据库...');
    
    // 首先初始化数据库
    const initResult = await wx.cloud.callFunction({
      name: 'initAnnouncementDatabase'
    });
    
    if (initResult.result && initResult.result.success) {
      console.log('✅ 数据库初始化成功');
    } else {
      console.log('ℹ️ 数据库可能已经初始化过了');
    }
    
    console.log('📝 开始创建测试公告...');
    
    // 创建测试公告
    for (let i = 0; i < testAnnouncements.length; i++) {
      const announcement = testAnnouncements[i];
      
      try {
        const result = await wx.cloud.callFunction({
          name: 'announcementManager',
          data: {
            action: 'create',
            title: announcement.title,
            content: announcement.content,
            type: announcement.type,
            priority: announcement.priority,
            status: announcement.status,
            publishTime: new Date(),
            effectiveTime: new Date(),
            expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
            isTop: announcement.isTop
          }
        });
        
        if (result.result && result.result.success) {
          console.log(`✅ 公告 ${i + 1} 创建成功: ${announcement.title}`);
          console.log(`   ID: ${result.result.data._id}`);
        } else {
          console.error(`❌ 公告 ${i + 1} 创建失败:`, result.result?.error);
        }
        
        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.error(`❌ 公告 ${i + 1} 创建异常:`, error);
      }
    }
    
    console.log('🔍 验证公告列表...');
    
    // 验证公告是否创建成功
    const listResult = await wx.cloud.callFunction({
      name: 'getAnnouncementList',
      data: {
        page: 1,
        pageSize: 10,
        status: 'active',
        isAdmin: false
      }
    });
    
    if (listResult.result && listResult.result.success) {
      const announcements = listResult.result.data.list;
      console.log('✅ 公告列表获取成功，共', announcements.length, '条公告');
      
      announcements.forEach((item, index) => {
        console.log(`📄 公告 ${index + 1}:`, {
          id: item._id,
          title: item.title,
          type: item.type,
          status: item.status,
          isTop: item.isTop,
          publishTime: item.publishTime
        });
      });
    } else {
      console.error('❌ 公告列表获取失败:', listResult.result?.error);
    }
    
    console.log('🎉 测试公告添加完成！');
    console.log('💡 现在您可以在小程序首页看到公告横幅了');
    
  } catch (error) {
    console.error('❌ 添加测试公告时发生异常:', error);
  }
}

// 执行添加测试公告
addTestAnnouncements();
