// 初始化密码数据库云函数
const cloud = require('wx-server-sdk');
const TimeZoneUtils = require('./common/timeZoneUtils');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  try {


    // 创建今天的初始记录（使用中国时区）
    const chinaTime = TimeZoneUtils.getChinaTime();
    const todayStr = TimeZoneUtils.getChinaDateString();
    const month = (chinaTime.getMonth() + 1).toString().padStart(2, '0');
    const day = chinaTime.getDate().toString().padStart(2, '0');
    const dateStr = `${month}月${day}日`;

    console.log('🔐 [初始化密码数据库] 中国时区今日:', todayStr, dateStr);



    // 直接创建记录，这会自动创建集合
    const initialRecord = {
      date: dateStr,
      locations: [
        { name: '零号大坝', code: '----' },
        { name: '长弓溪谷', code: '----' },
        { name: '巴克什', code: '----' },
        { name: '航天基地', code: '----' },
        { name: '潮汐监狱', code: '----' }
      ],
      foundCount: 0,
      totalCount: 5,
      updateTime: TimeZoneUtils.createStorageTime(),
      source: 'initialization',
      success: false
    };

    // 创建记录（这会自动创建集合）
    const createResult = await db.collection('daily_passwords').add({
      data: initialRecord
    });

    console.log('🔐 [数据库初始化] 创建初始密码记录成功:', createResult._id);

    return {
      success: true,
      message: '密码数据库初始化完成',
      collectionCreated: true,
      initialRecordCreated: true,
      recordId: createResult._id
    };
    
  } catch (error) {
    console.error('🔐 [数据库初始化] 初始化失败:', error);
    return {
      success: false,
      message: '数据库初始化失败',
      error: error.message
    };
  }
};
