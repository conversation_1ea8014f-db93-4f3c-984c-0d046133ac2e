// 客户确认订单完成页面
import API from '../../../utils/api.js';

const app = getApp();

Page({
  data: {
    orderId: '',
    orderInfo: null,
    completionProof: null,
    loading: true,
    confirming: false,
    disputing: false,
    
    // 仲裁原因选项
    disputeReasons: [
      { value: 'incomplete', label: '任务未完成' },
      { value: 'poor_quality', label: '服务质量差' },
      { value: 'not_as_described', label: '与描述不符' },
      { value: 'fake_proof', label: '证明材料造假' },
      { value: 'other', label: '其他原因' }
    ],
    selectedReason: '',
    customReason: '',
    
    // 自动确认倒计时
    autoConfirmTime: null,
    countdown: '',
    countdownTimer: null
  },

  onLoad(options) {
    if (options.orderId) {
      this.setData({
        orderId: options.orderId
      });
      this.loadOrderInfo();
      this.startCountdown();
    } else {
      wx.showToast({
        title: '订单ID缺失',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onUnload() {
    // 清理定时器
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
  },

  // 加载订单信息
  async loadOrderInfo() {
    try {
      this.setData({ loading: true });
      
      const result = await API.getOrderDetail(this.data.orderId);
      
      if (result.success && result.data) {
        const orderInfo = result.data;
        
        // 检查订单状态
        if (orderInfo.status !== 'proof_submitted') {
          wx.showModal({
            title: '提示',
            content: '订单状态不正确，无法进行确认操作',
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
          return;
        }
        
        // 检查用户权限（只有客户可以确认）
        const currentUser = app.globalData.userInfo;
        if (!currentUser || currentUser._id !== orderInfo.customerId) {
          wx.showModal({
            title: '权限不足',
            content: '只有订单发布者可以确认订单完成',
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
          return;
        }
        
        this.setData({
          orderInfo: orderInfo,
          completionProof: orderInfo.completionProof,
          autoConfirmTime: orderInfo.autoConfirmTime ? new Date(orderInfo.autoConfirmTime) : null,
          loading: false
        });
        
      } else {
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载订单信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 开始倒计时
  startCountdown() {
    if (!this.data.autoConfirmTime) return;
    
    const timer = setInterval(() => {
      const now = new Date();
      const autoConfirmTime = new Date(this.data.autoConfirmTime);
      const diff = autoConfirmTime - now;
      
      if (diff <= 0) {
        this.setData({
          countdown: '即将自动确认',
          countdownTimer: null
        });
        clearInterval(timer);
        return;
      }
      
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);
      
      this.setData({
        countdown: `${hours}小时${minutes}分${seconds}秒后自动确认`
      });
    }, 1000);
    
    this.setData({
      countdownTimer: timer
    });
  },

  // 预览图片
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const images = this.data.completionProof.images;
    
    if (images && images.length > 0) {
      const urls = images.map(img => img.fileID);
      wx.previewImage({
        current: urls[index],
        urls: urls
      });
    }
  },

  // 播放视频
  playVideo(e) {
    // 视频会自动播放，这里可以添加额外的逻辑
    console.log('播放视频');
  },

  // 确认订单完成
  confirmOrder() {
    wx.showModal({
      title: '确认完成',
      content: '确认接单者已完成任务？确认后将进行资金结算。',
      success: async (res) => {
        if (res.confirm) {
          await this.doConfirmOrder();
        }
      }
    });
  },

  async doConfirmOrder() {
    if (this.data.confirming) return;
    
    this.setData({ confirming: true });
    
    try {
      app.utils.showLoading('确认中...');
      
      const result = await API.confirmOrderCompletion(this.data.orderId);
      
      if (result.success) {
        app.utils.showSuccess('订单确认成功');
        
        // 清理定时器
        if (this.data.countdownTimer) {
          clearInterval(this.data.countdownTimer);
        }
        
        // 延迟返回
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        app.utils.showError(result.error || '确认失败');
      }
      
    } catch (error) {
      console.error('确认订单失败:', error);
      app.utils.showError('确认失败');
    } finally {
      app.utils.hideLoading();
      this.setData({ confirming: false });
    }
  },

  // 申请仲裁
  disputeOrder() {
    wx.showActionSheet({
      itemList: this.data.disputeReasons.map(item => item.label),
      success: (res) => {
        const selectedReason = this.data.disputeReasons[res.tapIndex];
        this.setData({
          selectedReason: selectedReason.value
        });
        
        if (selectedReason.value === 'other') {
          this.showCustomReasonInput();
        } else {
          this.confirmDispute(selectedReason.label);
        }
      }
    });
  },

  // 显示自定义原因输入
  showCustomReasonInput() {
    wx.showModal({
      title: '请输入仲裁原因',
      editable: true,
      placeholderText: '请详细说明问题...',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          this.setData({
            customReason: res.content.trim()
          });
          this.confirmDispute(res.content.trim());
        }
      }
    });
  },

  // 确认申请仲裁
  confirmDispute(reason) {
    wx.showModal({
      title: '申请仲裁',
      content: `确认申请仲裁？\n原因：${reason}`,
      success: async (res) => {
        if (res.confirm) {
          await this.doDispute(reason);
        }
      }
    });
  },

  async doDispute(reason) {
    if (this.data.disputing) return;
    
    this.setData({ disputing: true });
    
    try {
      app.utils.showLoading('提交中...');
      
      const result = await API.disputeOrder(this.data.orderId, reason);
      
      if (result.success) {
        app.utils.showSuccess('仲裁申请已提交');
        
        // 清理定时器
        if (this.data.countdownTimer) {
          clearInterval(this.data.countdownTimer);
        }
        
        // 延迟返回
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        app.utils.showError(result.error || '申请失败');
      }
      
    } catch (error) {
      console.error('申请仲裁失败:', error);
      app.utils.showError('申请失败');
    } finally {
      app.utils.hideLoading();
      this.setData({ disputing: false });
    }
  },

  // 格式化时间
  formatTime(time) {
    if (!time) return '';

    const date = new Date(time);
    if (isNaN(date.getTime())) {
      console.warn('🕐 [确认凭证] Invalid date:', time);
      return '';
    }

    // 🔧 修复时区一致性问题：
    // 存储的时间是UTC时间，需要转换为中国时区时间进行显示
    let displayDate = date;
    const chinaOffset = 8 * 60; // 中国时区偏移量（分钟）

    if (typeof time === 'string' && time.includes('T') && time.includes('Z')) {
      // 这是UTC时间字符串，转换为中国时区时间进行显示
      displayDate = new Date(date.getTime() + chinaOffset * 60 * 1000);
    }

    return `${displayDate.getFullYear()}-${(displayDate.getMonth() + 1).toString().padStart(2, '0')}-${displayDate.getDate().toString().padStart(2, '0')} ${displayDate.getHours().toString().padStart(2, '0')}:${displayDate.getMinutes().toString().padStart(2, '0')}`;
  }
});
