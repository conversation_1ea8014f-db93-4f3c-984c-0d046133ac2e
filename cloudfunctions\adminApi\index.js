const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 统一时区处理工具
const TimeZoneUtils = {
  // 中国时区偏移量（分钟）
  CHINA_OFFSET: 8 * 60,

  /**
   * 获取当前中国时间
   * @returns {Date} 中国时区的当前时间
   */
  getChinaTime() {
    const now = new Date();
    return new Date(now.getTime() + this.CHINA_OFFSET * 60 * 1000);
  },

  /**
   * 获取中国时区的今日开始时间（00:00:00）
   * @returns {Date} 中国时区今日开始时间
   */
  getChinaTodayStart() {
    const chinaTime = this.getChinaTime();
    return new Date(chinaTime.getFullYear(), chinaTime.getMonth(), chinaTime.getDate(), 0, 0, 0, 0);
  },

  /**
   * 获取用于数据库查询的UTC时间
   * @param {Date} chinaTime 中国时区时间
   * @returns {Date} 对应的UTC时间
   */
  chinaTimeToUTC(chinaTime) {
    return new Date(chinaTime.getTime() - this.CHINA_OFFSET * 60 * 1000);
  },

  /**
   * 获取今日开始时间的UTC版本（用于数据库查询）
   * @returns {Date} 今日开始时间的UTC版本
   */
  getTodayStartUTC() {
    const todayStart = this.getChinaTodayStart();
    return this.chinaTimeToUTC(todayStart);
  },

  /**
   * 获取指定时间前的UTC时间（用于数据库查询）
   * @param {number} minutes 分钟数
   * @returns {Date} 指定时间前的UTC时间
   */
  getTimeAgoUTC(minutes) {
    const chinaTime = this.getChinaTime();
    const timeAgo = new Date(chinaTime.getTime() - minutes * 60 * 1000);
    return this.chinaTimeToUTC(timeAgo);
  },

  /**
   * 创建用于存储的时间（统一使用中国时区时间转换为UTC存储）
   * @param {Date} [time] 可选的时间，默认为当前时间
   * @returns {Date} 用于存储的UTC时间
   */
  createStorageTime(time) {
    const chinaTime = time || this.getChinaTime();
    return this.chinaTimeToUTC(chinaTime);
  }
};

// CORS响应头
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400'
};

// 主函数入口
exports.main = async (event, context) => {
  // 处理OPTIONS预检请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // 解析请求体
    let requestData;
    if (typeof event.body === 'string') {
      requestData = JSON.parse(event.body);
    } else {
      requestData = event.body || event;
    }

    const { action, data } = requestData;
    
    // 验证管理员权限（简化版本，实际项目中应该更严格）
    if (action !== 'adminLogin' && !await verifyAdminToken(event.headers?.authorization)) {
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          error: '权限不足'
        })
      };
    }

    let result;
    
    // 根据action分发到不同的处理函数
    switch (action) {
      // 认证相关
      case 'adminLogin':
        result = await handleAdminLogin(data);
        break;
      case 'verifyToken':
        result = await handleVerifyToken(event.headers?.authorization);
        break;
      case 'adminLogout':
        result = await handleAdminLogout();
        break;

      // 仪表盘相关
      case 'getDashboardStats':
        result = await handleGetDashboardStats();
        break;
      case 'getChartData':
        result = await handleGetChartData(data);
        break;
      case 'getRecentActivities':
        result = await handleGetRecentActivities();
        break;

      // 用户管理
      case 'getUserList':
        result = await handleGetUserList(data);
        break;
      case 'getUserDetail':
        result = await handleGetUserDetail(data);
        break;
      case 'updateUserStatus':
        result = await handleUpdateUserStatus(data);
        break;
      case 'getUserStats':
        result = await handleGetUserStats();
        break;

      // 订单管理
      case 'getOrderList':
        result = await handleGetOrderList(data);
        break;
      case 'getOrderDetail':
        result = await handleGetOrderDetail(data);
        break;
      case 'updateOrderStatus':
        result = await handleUpdateOrderStatus(data);
        break;
      case 'getOrderStats':
        result = await handleGetOrderStats();
        break;

      // 评价管理
      case 'getEvaluationList':
        result = await handleGetEvaluationList(data);
        break;
      case 'approveEvaluation':
        result = await handleApproveEvaluation(data);
        break;
      case 'rejectEvaluation':
        result = await handleRejectEvaluation(data);
        break;
      case 'getEvaluationStats':
        result = await handleGetEvaluationStats();
        break;

      // 钱包管理
      case 'getTransactionList':
        result = await handleGetTransactionList(data);
        break;
      case 'approveWithdraw':
        result = await handleApproveWithdraw(data);
        break;
      case 'rejectWithdraw':
        result = await handleRejectWithdraw(data);
        break;
      case 'getWalletStats':
        result = await handleGetWalletStats();
        break;

      // 通知管理
      case 'getNotificationList':
        result = await handleGetNotificationList(data);
        break;
      case 'createNotification':
        result = await handleCreateNotification(data);
        break;
      case 'deleteNotification':
        result = await handleDeleteNotification(data);
        break;

      // 聊天管理
      case 'getChatRoomList':
        result = await handleGetChatRoomList(data);
        break;
      case 'getChatMessages':
        result = await handleGetChatMessages(data);
        break;
      case 'banUser':
        result = await handleBanUser(data);
        break;
      case 'getChatStats':
        result = await handleGetChatStats();
        break;

      default:
        result = {
          success: false,
          error: '未知的操作类型'
        };
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(result)
    };

  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message || '服务器内部错误'
      })
    };
  }
};

// 验证管理员token
async function verifyAdminToken(authorization) {
  if (!authorization) return false;

  const token = authorization.replace('Bearer ', '');
  // 实际项目中应该验证JWT token或查询管理员表
  // 这里简化为检查token长度，实际应该实现真正的验证逻辑
  return token.length > 10;
}

// 管理员登录
async function handleAdminLogin(data) {
  const { username, password } = data;

  // 实际项目中应该查询管理员表并验证密码哈希
  // 这里返回错误，要求实现真正的管理员认证系统
  return {
    success: false,
    error: '管理员认证系统需要实现，请联系开发人员配置管理员账户'
  };
}

// 验证token
async function handleVerifyToken(authorization) {
  const isValid = await verifyAdminToken(authorization);
  
  if (isValid) {
    return {
      success: true,
      data: {
        admin: {
          id: 'admin_001',
          username: 'admin',
          email: '<EMAIL>',
          role: 'super_admin'
        }
      }
    };
  }
  
  return {
    success: false,
    error: 'Token无效'
  };
}

// 管理员登出
async function handleAdminLogout() {
  return {
    success: true,
    message: '登出成功'
  };
}

// 获取仪表盘统计数据
async function handleGetDashboardStats() {
  try {
    console.log('开始获取仪表盘统计数据...');

    // 获取今日开始时间（使用统一时区处理工具）
    const now = new Date();
    const chinaTime = TimeZoneUtils.getChinaTime();
    const todayStart = TimeZoneUtils.getChinaTodayStart();
    const todayStartUTC = TimeZoneUtils.getTodayStartUTC();

    console.log('🕐 当前时间(服务器):', now.toISOString());
    console.log('🕐 中国本地时间:', chinaTime.toISOString());
    console.log('🕐 今日开始时间(中国):', todayStart.toISOString());
    console.log('🕐 今日开始时间(UTC):', todayStartUTC.toISOString());
    console.log('🕐 当前时区偏移:', now.getTimezoneOffset(), '分钟');

    // 获取用户统计
    console.log('获取用户统计...');
    const userStats = await db.collection('users').count();

    // 获取今日新用户
    console.log('🔍 查询今日新用户，todayStartUTC:', todayStartUTC);
    const newUsersToday = await db.collection('users')
      .where({
        createTime: _.gte(todayStartUTC)
      })
      .count();
    console.log('📊 今日新用户查询结果:', newUsersToday);

    // 获取活跃用户（7天内登录）
    const activeUsers = await db.collection('users')
      .where({
        lastLoginTime: _.gte(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
      })
      .count();

    // 获取订单统计
    console.log('获取订单统计...');
    const orderStats = await db.collection('orders').count();

    // 获取今日订单 - 使用正确的时区
    console.log('🔍 查询今日订单，todayStartUTC:', todayStartUTC);

    // 方式1：使用 _.gte
    const todayOrders1 = await db.collection('orders')
      .where({
        createTime: _.gte(todayStartUTC)
      })
      .count();
    console.log('📊 方式1 (_.gte) 今日订单查询结果:', todayOrders1);

    // 方式2：使用 db.command.gte
    const todayOrders2 = await db.collection('orders')
      .where({
        createTime: db.command.gte(todayStartUTC)
      })
      .count();
    console.log('📊 方式2 (db.command.gte) 今日订单查询结果:', todayOrders2);

    // 方式3：使用范围查询
    const todayEndUTC = new Date(todayStartUTC.getTime() + 24 * 60 * 60 * 1000 - 1);
    const todayOrders3 = await db.collection('orders')
      .where({
        createTime: _.gte(todayStartUTC).and(_.lte(todayEndUTC))
      })
      .count();
    console.log('📊 方式3 (范围查询) 今日订单查询结果:', todayOrders3);
    console.log('🕐 今日结束时间(UTC):', todayEndUTC.toISOString());

    // 使用方式2的结果
    const todayOrders = todayOrders2;

    // 调试：获取最近几个订单的创建时间和更新时间
    const recentOrders = await db.collection('orders')
      .orderBy('createTime', 'desc')
      .limit(10)
      .field({
        createTime: true,
        updateTime: true,
        orderNo: true,
        status: true
      })
      .get();
    console.log('🔍 最近10个订单的时间信息:', recentOrders.data.map(order => ({
      orderNo: order.orderNo,
      createTime: order.createTime,
      updateTime: order.updateTime,
      createTimeStr: order.createTime ? order.createTime.toISOString() : 'null',
      updateTimeStr: order.updateTime ? order.updateTime.toISOString() : 'null',
      createTimeType: typeof order.createTime,
      updateTimeType: typeof order.updateTime,
      isCreateTimeToday: order.createTime >= todayStartUTC,
      isUpdateTimeToday: order.updateTime >= todayStartUTC,
      status: order.status,
      todayStartStr: todayStartUTC.toISOString()
    })));

    // 特别检查今天创建的订单
    const todayOrdersManual = recentOrders.data.filter(order => {
      if (!order.createTime) return false;
      const orderDate = new Date(order.createTime);
      const todayDate = new Date(todayStartUTC);
      console.log('🔍 订单时间比较:', {
        orderNo: order.orderNo,
        orderTime: orderDate.toISOString(),
        todayStartUTC: todayDate.toISOString(),
        orderTimestamp: orderDate.getTime(),
        todayTimestamp: todayDate.getTime(),
        isAfterToday: orderDate >= todayDate
      });
      return orderDate >= todayDate;
    });
    console.log('🔍 手动筛选的今日订单:', todayOrdersManual.length, '个');

    // 额外测试：查找包含"2025-07-28"的订单
    const testTodayOrders = await db.collection('orders')
      .where({
        createTime: _.gte(new Date('2025-07-28T00:00:00.000Z'))
      })
      .get();
    console.log('🧪 测试查询今日订单(createTime UTC):', testTodayOrders.data.length, '个');

    // 测试：查找包含今天日期的订单（考虑时区）
    const testLocalTodayOrders = await db.collection('orders')
      .where({
        createTime: _.gte(new Date('2025-07-28T00:00:00+08:00'))
      })
      .get();
    console.log('🧪 测试查询今日订单(createTime +8时区):', testLocalTodayOrders.data.length, '个');

    // 测试：按updateTime查询今日订单
    const testUpdateTimeOrders = await db.collection('orders')
      .where({
        updateTime: _.gte(todayStartUTC)
      })
      .get();
    console.log('🧪 测试查询今日订单(updateTime):', testUpdateTimeOrders.data.length, '个');

    // 保存测试结果到返回数据中
    const testResults = {
      createTimeUtcTest: testTodayOrders.data.length,
      createTimeLocalTest: testLocalTodayOrders.data.length,
      updateTimeTest: testUpdateTimeOrders.data.length,
      firstOrderCreateTime: recentOrders.data.length > 0 && recentOrders.data[0].createTime ?
        recentOrders.data[0].createTime.toISOString() : 'no orders',
      firstOrderUpdateTime: recentOrders.data.length > 0 && recentOrders.data[0].updateTime ?
        recentOrders.data[0].updateTime.toISOString() : 'no updateTime'
    };

    // 调试：获取所有订单总数
    const allOrdersCount = await db.collection('orders').count();
    console.log('📊 数据库中订单总数:', allOrdersCount.total);

    // 调试：手动计算今日订单
    const manualTodayCount = recentOrders.data.filter(order =>
      order.createTime && order.createTime >= todayStart
    ).length;
    console.log('🔍 手动计算的今日订单数:', manualTodayCount);

    // 获取已完成订单
    const completedOrders = await db.collection('orders')
      .where({ status: 'completed' })
      .count();

    // 获取收入统计（使用pricing.totalAmount字段）
    console.log('获取收入统计...');
    // 计算所有订单的收入（包括已取消的，用于统计分析）
    const revenueResult = await db.collection('orders')
      .field({ 'pricing.totalAmount': true, reward: true, status: true })
      .get();

    console.log('📊 收入计算 - 订单总数:', revenueResult.data.length);

    let totalRevenue = 0;
    let completedRevenue = 0;
    let cancelledRevenue = 0;

    revenueResult.data.forEach(order => {
      // 优先使用pricing.totalAmount，其次使用reward
      const amount = order.pricing?.totalAmount || order.reward || 0;
      totalRevenue += amount;

      // 分别统计不同状态的收入
      if (order.status === 'completed') {
        completedRevenue += amount;
      } else if (order.status === 'cancelled') {
        cancelledRevenue += amount;
      }
    });

    console.log('💰 收入统计:');
    console.log('  - 总收入:', totalRevenue, '分');
    console.log('  - 已完成订单收入:', completedRevenue, '分');
    console.log('  - 已取消订单收入:', cancelledRevenue, '分');

    // 获取评价统计
    console.log('获取评价统计...');
    const evaluationStats = await db.collection('evaluations').count();
    const avgScoreResult = await db.collection('evaluations')
      .field({ score: true })
      .get();

    const avgScore = avgScoreResult.data.length > 0
      ? avgScoreResult.data.reduce((sum, eval) => sum + (eval.score || 0), 0) / avgScoreResult.data.length
      : 0;

    // 获取待审核评价数量
    const pendingEvaluations = await db.collection('evaluations')
      .where({ status: 'pending' })
      .count();

    const result = {
      success: true,
      data: {
        totalUsers: userStats.total || 0,
        activeUsers: activeUsers.total || 0,
        totalOrders: orderStats.total || 0,
        completedOrders: completedOrders.total || 0,
        totalRevenue: totalRevenue,
        averageScore: Number(avgScore.toFixed(1)),
        newUsersToday: newUsersToday.total || 0,
        ordersToday: todayOrders.total || 0,
        pendingEvaluations: pendingEvaluations.total || 0,
        evaluationCount: evaluationStats.total || 0
      },
      debug: {
        currentTime: now.toISOString(),
        chinaTime: chinaTime.toISOString(),
        todayStartChina: todayStart.toISOString(),
        todayStartUTC: todayStartUTC.toISOString(),
        timezoneOffset: now.getTimezoneOffset(),
        queryResults: {
          method1: todayOrders1.total || 0,
          method2: todayOrders2.total || 0,
          method3: todayOrders3.total || 0
        },
        recentOrdersCount: recentOrders.data.length,
        manualTodayCount: recentOrders.data.filter(order =>
          order.createTime && order.createTime >= todayStartUTC
        ).length,
        testResults: testResults
      }
    };

    console.log('仪表盘统计数据获取成功:', result);
    console.log('🔍 最终返回的今日订单数:', result.data.ordersToday);
    console.log('🔍 最终返回的今日新用户数:', result.data.newUsersToday);
    return result;

  } catch (error) {
    console.error('获取仪表盘统计失败:', error);

    // 返回错误信息，不再使用模拟数据
    return {
      success: false,
      error: '获取仪表盘统计失败: ' + error.message
    };
  }
}

// 获取图表数据
async function handleGetChartData(data) {
  try {
    const { type } = data || {};
    console.log('获取图表数据，类型:', type);

    // 生成最近7天的日期（使用中国时区）
    const generateLast7Days = () => {
      const days = [];
      for (let i = 6; i >= 0; i--) {
        // 使用中国时区的当前时间
        const now = new Date();
        const chinaTime = new Date(now.getTime() + (8 * 60 * 60 * 1000)); // UTC+8
        chinaTime.setDate(chinaTime.getDate() - i);
        chinaTime.setHours(0, 0, 0, 0);

        // 转换回UTC时间用于数据库查询
        const utcTime = new Date(chinaTime.getTime() - (8 * 60 * 60 * 1000));

        days.push({
          date: chinaTime.toISOString().split('T')[0],
          timestamp: utcTime.getTime()
        });
      }
      return days;
    };

    const last7Days = generateLast7Days();
    let chartData = [];

    if (type === 'order') {
      // 获取最近7天的订单数据（包含数量和金额）
      for (const day of last7Days) {
        const dayStart = new Date(day.timestamp);
        const dayEnd = new Date(day.timestamp + 24 * 60 * 60 * 1000);

        console.log('🔍 查询日期:', day.date, '开始:', dayStart.toISOString(), '结束:', dayEnd.toISOString());

        // 获取当天的订单数量
        const dayOrdersCount = await db.collection('orders')
          .where({
            createTime: _.gte(dayStart).and(_.lt(dayEnd))
          })
          .count();

        // 获取当天的订单详情（用于计算金额）
        const dayOrdersData = await db.collection('orders')
          .where({
            createTime: _.gte(dayStart).and(_.lt(dayEnd))
          })
          .field({
            reward: true
          })
          .get();

        // 计算当天订单总金额
        const totalAmount = dayOrdersData.data.reduce((sum, order) => {
          return sum + (order.reward || 0);
        }, 0);

        console.log('📊 日期', day.date, '订单数:', dayOrdersCount.total, '总金额:', totalAmount);

        // 使用中国时区的日期格式
        const chinaDate = new Date(day.timestamp + 8 * 60 * 60 * 1000);
        const month = chinaDate.getMonth() + 1;
        const dayNum = chinaDate.getDate();
        const dateStr = `${month}/${dayNum}`;

        chartData.push({
          date: dateStr,
          count: dayOrdersCount.total || 0,
          amount: totalAmount || 0
        });
      }
    } else if (type === 'user') {
      // 获取最近7天的用户注册数据
      for (const day of last7Days) {
        const nextDay = day.timestamp + 24 * 60 * 60 * 1000;
        const dayUsers = await db.collection('users')
          .where({
            createTime: _.gte(day.timestamp).and(_.lt(nextDay))
          })
          .count();

        chartData.push({
          date: day.date,
          value: dayUsers.total || 0
        });
      }
    } else {
      // 默认返回空数据，不使用随机数
      chartData = last7Days.map(day => ({
        date: day.date,
        value: 0
      }));
    }

    console.log('图表数据获取成功:', chartData);
    return {
      success: true,
      data: {
        chartData: chartData
      }
    };

  } catch (error) {
    console.error('获取图表数据失败:', error);

    // 返回错误信息，不再使用模拟数据
    return {
      success: false,
      error: '获取图表数据失败: ' + error.message
    };
  }
}

// 获取最近活动
async function handleGetRecentActivities() {
  try {
    console.log('获取最近活动数据...');

    const activities = [];
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // 获取最近24小时的订单活动
    const recentOrders = await db.collection('orders')
      .where('createTime', '>=', oneDayAgo)
      .orderBy('createTime', 'desc')
      .limit(5)
      .get();

    recentOrders.data.forEach((order) => {
      // 过滤掉测试数据和无意义的标题
      const title = order.title || '';
      if (title && title.length > 2 && !title.match(/^(测试|test|123|111|000)/i)) {
        activities.push({
          id: `order_${order._id}`,
          type: 'order',
          description: `新订单：${title.length > 20 ? title.substring(0, 20) + '...' : title}`,
          timestamp: order.createTime ? new Date(order.createTime).toISOString() : new Date().toISOString()
        });
      }
    });

    // 获取最近24小时的用户注册活动
    const recentUsers = await db.collection('users')
      .where('createTime', '>=', oneDayAgo)
      .orderBy('createTime', 'desc')
      .limit(3)
      .get();

    recentUsers.data.forEach((user) => {
      const nickName = user.nickName || user.nickname || '';
      // 过滤掉测试用户
      if (nickName && !nickName.match(/^(测试|test|admin|管理)/i)) {
        activities.push({
          id: `user_${user._id}`,
          type: 'user',
          description: `新用户注册：${nickName}`,
          timestamp: user.createTime ? new Date(user.createTime).toISOString() : new Date().toISOString()
        });
      }
    });

    // 获取最近24小时的交易活动
    const recentTransactions = await db.collection('transactions')
      .where('createTime', '>=', oneDayAgo)
      .orderBy('createTime', 'desc')
      .limit(3)
      .get();

    recentTransactions.data.forEach((transaction) => {
      if (transaction.amount && transaction.amount > 0) {
        const amount = (transaction.amount / 100).toFixed(2);
        const type = transaction.type === 'recharge' ? '充值' :
                    transaction.type === 'withdraw' ? '提现' : '交易';
        activities.push({
          id: `transaction_${transaction._id}`,
          type: 'payment',
          description: `${type}：¥${amount}`,
          timestamp: transaction.createTime ? new Date(transaction.createTime).toISOString() : new Date().toISOString()
        });
      }
    });

    // 如果没有真实活动数据，生成一些基于统计的活动信息
    if (activities.length === 0) {
      // 获取今日统计数据
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);

      const [todayOrders, todayUsers] = await Promise.all([
        db.collection('orders').where('createTime', '>=', todayStart).count(),
        db.collection('users').where('createTime', '>=', todayStart).count()
      ]);

      if (todayOrders.total > 0) {
        activities.push({
          id: 'summary_orders',
          type: 'order',
          description: `今日新增订单 ${todayOrders.total} 个`,
          timestamp: new Date(now.getTime() - 30 * 60 * 1000).toISOString()
        });
      }

      if (todayUsers.total > 0) {
        activities.push({
          id: 'summary_users',
          type: 'user',
          description: `今日新增用户 ${todayUsers.total} 人`,
          timestamp: new Date(now.getTime() - 60 * 60 * 1000).toISOString()
        });
      }

      // 添加系统状态活动
      activities.push({
        id: 'system_status',
        type: 'message',
        description: '系统运行正常，数据同步完成',
        timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString()
      });
    }

    // 按时间排序
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    console.log('最近活动数据获取成功:', activities.slice(0, 8));
    return {
      success: true,
      data: {
        activities: activities.slice(0, 8) // 最多返回8条
      }
    };

  } catch (error) {
    console.error('获取最近活动失败:', error);

    // 返回基本的系统活动信息
    return {
      success: true,
      data: {
        activities: [
          {
            id: 'system_1',
            type: 'message',
            description: '系统运行正常',
            timestamp: new Date().toISOString()
          },
          {
            id: 'system_2',
            type: 'message',
            description: '数据库连接正常',
            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
          }
        ]
      }
    };
  }
}

// 获取用户列表
async function handleGetUserList(data) {
  try {
    const { page = 1, limit = 20, search, status } = data || {};
    
    let query = db.collection('users');
    
    // 添加搜索条件
    if (search) {
      query = query.where({
        nickname: db.RegExp({
          regexp: search,
          options: 'i'
        })
      });
    }
    
    // 添加状态筛选
    if (status && status !== 'all') {
      query = query.where({ status });
    }
    
    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createdAt', 'desc')
      .get();
    
    const total = await query.count();
    
    const totalPages = Math.ceil(total.total / limit);

    return {
      success: true,
      data: {
        users: result.data,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total.total,
          pageSize: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    };
  } catch (error) {
    console.error('获取用户列表失败:', error);
    return {
      success: false,
      error: '获取用户列表失败'
    };
  }
}

// 获取用户详情
async function handleGetUserDetail(data) {
  try {
    const { userId } = data;
    
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空'
      };
    }

    const result = await db.collection('users').doc(userId).get();
    
    if (!result.data) {
      return {
        success: false,
        error: '用户不存在'
      };
    }

    const user = result.data;
    
    return {
      success: true,
      data: {
        _id: user._id,
        openid: user.openid,
        nickname: user.nickName || user.nickname,
        name: user.name,
        avatar: user.avatarUrl || user.avatar,
        avatarUrl: user.avatarUrl,
        phone: user.phone,
        isVerified: user.isVerified || false,
        status: user.status || 'active',
        createTime: user.createTime,
        updateTime: user.updateTime,
        balance: user.balance || 0,
        creditScore: user.creditScore || 0,
        gameNickName: user.gameNickName,
        bio: user.bio,
        contactInfo: user.contactInfo,
        settings: user.settings,
        orderCount: user.orderCount || 0
      }
    };
  } catch (error) {
    console.error('获取用户详情失败:', error);
    return {
      success: false,
      error: '获取用户详情失败'
    };
  }
}

// 更新用户状态
async function handleUpdateUserStatus(data) {
  try {
    const { userId, status } = data;
    
    await db.collection('users').doc(userId).update({
      status,
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '用户状态更新成功'
    };
  } catch (error) {
    console.error('更新用户状态失败:', error);
    return {
      success: false,
      error: '更新用户状态失败'
    };
  }
}

// 获取用户统计
async function handleGetUserStats() {
  try {
    const totalUsers = await db.collection('users').count();
    const activeUsers = await db.collection('users').where({ status: 'active' }).count();
    const newUsersThisMonth = await db.collection('users')
      .where({
        createdAt: _.gte(new Date(new Date().getFullYear(), new Date().getMonth(), 1))
      })
      .count();
    
    return {
      success: true,
      data: {
        total: totalUsers.total,
        active: activeUsers.total,
        newThisMonth: newUsersThisMonth.total
      }
    };
  } catch (error) {
    console.error('获取用户统计失败:', error);
    return {
      success: false,
      error: '获取用户统计失败'
    };
  }
}

// 获取订单列表
async function handleGetOrderList(data) {
  try {
    console.log('获取订单列表，参数:', data);
    const { page = 1, limit = 20, status, search } = data || {};

    // 首先获取所有订单来检查数据结构
    const allOrdersResult = await db.collection('orders').limit(5).get();
    console.log('订单数据样例:', allOrdersResult.data);

    let query = db.collection('orders');

    // 状态过滤
    if (status && status !== 'all') {
      console.log('按状态过滤:', status);
      query = query.where('status', '==', status);
    }

    // 搜索过滤
    if (search && search.trim()) {
      console.log('按标题搜索:', search);
      // 使用正则表达式搜索标题
      query = query.where('title', 'regexp', new RegExp(search, 'i'));
    }

    // 获取总数（在分页之前）
    const countResult = await query.count();
    const totalItems = countResult.total;
    console.log('符合条件的订单总数:', totalItems);

    // 分页查询
    const skip = (page - 1) * limit;
    console.log('分页参数:', { page, limit, skip });

    // 尝试不同的时间字段进行排序
    let result;
    try {
      // 首先尝试 createTime
      result = await query
        .skip(skip)
        .limit(limit)
        .orderBy('createTime', 'desc')
        .get();
    } catch (error1) {
      console.log('createTime排序失败，尝试createdAt:', error1.message);
      try {
        // 如果失败，尝试 createdAt
        result = await query
          .skip(skip)
          .limit(limit)
          .orderBy('createdAt', 'desc')
          .get();
      } catch (error2) {
        console.log('createdAt排序失败，尝试_id:', error2.message);
        // 如果都失败，使用 _id 排序
        result = await query
          .skip(skip)
          .limit(limit)
          .orderBy('_id', 'desc')
          .get();
      }
    }

    console.log('查询结果:', {
      count: result.data.length,
      totalItems,
      firstOrder: result.data[0]
    });

    const totalPages = Math.ceil(totalItems / limit);

    return {
      success: true,
      data: {
        orders: result.data,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          pageSize: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    };
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return {
      success: false,
      error: '获取订单列表失败: ' + error.message,
      details: error.toString()
    };
  }
}

// 获取订单详情
async function handleGetOrderDetail(data) {
  try {
    const { orderId } = data;
    const result = await db.collection('orders').doc(orderId).get();
    
    return {
      success: true,
      data: {
        order: result.data
      }
    };
  } catch (error) {
    console.error('获取订单详情失败:', error);
    return {
      success: false,
      error: '获取订单详情失败'
    };
  }
}

// 更新订单状态
async function handleUpdateOrderStatus(data) {
  try {
    const { orderId, status } = data;
    
    await db.collection('orders').doc(orderId).update({
      status,
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '订单状态更新成功'
    };
  } catch (error) {
    console.error('更新订单状态失败:', error);
    return {
      success: false,
      error: '更新订单状态失败'
    };
  }
}

// 获取订单统计
async function handleGetOrderStats() {
  try {
    const totalOrders = await db.collection('orders').count();
    const pendingOrders = await db.collection('orders').where({ status: 'pending' }).count();
    const completedOrders = await db.collection('orders').where({ status: 'completed' }).count();
    
    return {
      success: true,
      data: {
        total: totalOrders.total,
        pending: pendingOrders.total,
        completed: completedOrders.total
      }
    };
  } catch (error) {
    console.error('获取订单统计失败:', error);
    return {
      success: false,
      error: '获取订单统计失败'
    };
  }
}

// 获取评价列表
async function handleGetEvaluationList(data) {
  try {
    const { page = 1, limit = 20, status } = data || {};

    let query = db.collection('evaluations');

    if (status && status !== 'all') {
      query = query.where({ status });
    }

    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createTime', 'desc')
      .get();

    const total = await query.count();

    // 获取所有相关用户ID
    const userIds = new Set();
    result.data.forEach(evaluation => {
      if (evaluation.evaluatorId) userIds.add(evaluation.evaluatorId);
      if (evaluation.customerId) userIds.add(evaluation.customerId);
      if (evaluation.accepterId) userIds.add(evaluation.accepterId);
    });

    // 批量获取用户信息
    const userInfoMap = {};
    if (userIds.size > 0) {
      const userIdsArray = Array.from(userIds);
      const userResults = await Promise.allSettled(
        userIdsArray.map(userId =>
          db.collection('users').doc(userId).get()
        )
      );

      userResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.data) {
          const userId = userIdsArray[index];
          const userData = result.value.data;
          console.log('获取到用户数据:', {
            userId,
            nickName: userData.nickName,
            avatarUrl: userData.avatarUrl
          });
          userInfoMap[userId] = {
            nickName: userData.nickName || userData.nickname || '未知用户',
            avatarUrl: userData.avatarUrl || '/placeholder.svg?height=32&width=32'
          };
        } else {
          console.log('获取用户信息失败:', {
            userId: userIdsArray[index],
            error: result.reason
          });
        }
      });
    }

    // 为评价数据添加用户信息
    const evaluationsWithUserInfo = result.data.map(evaluation => {
      console.log('处理评价数据:', {
        evaluationId: evaluation._id,
        evaluatorId: evaluation.evaluatorId,
        customerId: evaluation.customerId,
        accepterId: evaluation.accepterId,
        evaluatorType: evaluation.evaluatorType
      });

      // 根据evaluatorType确定评价者和被评价者
      let evaluatorInfo, evaluatedInfo;

      if (evaluation.evaluatorType === 'customer') {
        // 客户评价接单者
        evaluatorInfo = userInfoMap[evaluation.customerId] || {
          nickName: '未知客户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
        evaluatedInfo = userInfoMap[evaluation.accepterId] || {
          nickName: '未知接单者',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
      } else if (evaluation.evaluatorType === 'accepter') {
        // 接单者评价客户
        evaluatorInfo = userInfoMap[evaluation.accepterId] || {
          nickName: '未知接单者',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
        evaluatedInfo = userInfoMap[evaluation.customerId] || {
          nickName: '未知客户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
      } else {
        // 兼容旧数据，使用evaluatorId
        evaluatorInfo = userInfoMap[evaluation.evaluatorId] || {
          nickName: '未知用户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
        evaluatedInfo = userInfoMap[evaluation.accepterId] || userInfoMap[evaluation.customerId] || {
          nickName: '未知用户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        };
      }

      console.log('用户信息映射结果:', {
        evaluatorInfo,
        evaluatedInfo
      });

      return {
        ...evaluation,
        evaluatorInfo,
        evaluatedInfo
      };
    });

    return {
      success: true,
      data: {
        evaluations: evaluationsWithUserInfo,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取评价列表失败:', error);
    return {
      success: false,
      error: '获取评价列表失败'
    };
  }
}

// 审核通过评价
async function handleApproveEvaluation(data) {
  try {
    const { evaluationId } = data;
    
    await db.collection('evaluations').doc(evaluationId).update({
      status: 'approved',
      reviewedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '评价审核通过'
    };
  } catch (error) {
    console.error('审核评价失败:', error);
    return {
      success: false,
      error: '审核评价失败'
    };
  }
}

// 拒绝评价
async function handleRejectEvaluation(data) {
  try {
    const { evaluationId, reason } = data;
    
    await db.collection('evaluations').doc(evaluationId).update({
      status: 'rejected',
      rejectReason: reason,
      reviewedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '评价已拒绝'
    };
  } catch (error) {
    console.error('拒绝评价失败:', error);
    return {
      success: false,
      error: '拒绝评价失败'
    };
  }
}

// 获取评价统计
async function handleGetEvaluationStats() {
  try {
    const totalEvaluations = await db.collection('evaluations').count();
    const pendingEvaluations = await db.collection('evaluations').where({ status: 'pending' }).count();
    
    const scoresResult = await db.collection('evaluations')
      .where({ status: 'approved' })
      .field({ score: true })
      .get();
    
    const averageScore = scoresResult.data.length > 0 
      ? scoresResult.data.reduce((sum, eval) => sum + eval.score, 0) / scoresResult.data.length 
      : 0;
    
    const lowScoreCount = scoresResult.data.filter(eval => eval.score <= 2).length;
    
    return {
      success: true,
      data: {
        total: totalEvaluations.total,
        pending: pendingEvaluations.total,
        averageScore: Number(averageScore.toFixed(1)),
        lowScoreCount
      }
    };
  } catch (error) {
    console.error('获取评价统计失败:', error);
    return {
      success: false,
      error: '获取评价统计失败'
    };
  }
}

// 获取交易记录列表
async function handleGetTransactionList(data) {
  try {
    const { page = 1, limit = 20, type, status } = data || {};
    
    let query = db.collection('transactions');
    
    if (type && type !== 'all') {
      query = query.where({ type });
    }
    
    if (status && status !== 'all') {
      query = query.where({ status });
    }
    
    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createdAt', 'desc')
      .get();
    
    const total = await query.count();
    
    return {
      success: true,
      data: {
        transactions: result.data,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取交易记录失败:', error);
    return {
      success: false,
      error: '获取交易记录失败'
    };
  }
}

// 审核提现申请
async function handleApproveWithdraw(data) {
  try {
    const { transactionId } = data;
    
    await db.collection('transactions').doc(transactionId).update({
      status: 'completed',
      processedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '提现申请已通过'
    };
  } catch (error) {
    console.error('审核提现失败:', error);
    return {
      success: false,
      error: '审核提现失败'
    };
  }
}

// 拒绝提现申请
async function handleRejectWithdraw(data) {
  try {
    const { transactionId, reason } = data;
    
    await db.collection('transactions').doc(transactionId).update({
      status: 'failed',
      rejectReason: reason,
      processedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '提现申请已拒绝'
    };
  } catch (error) {
    console.error('拒绝提现失败:', error);
    return {
      success: false,
      error: '拒绝提现失败'
    };
  }
}

// 获取钱包统计
async function handleGetWalletStats() {
  try {
    const totalTransactions = await db.collection('transactions').count();
    const pendingWithdraws = await db.collection('transactions')
      .where({ type: 'withdraw', status: 'pending' })
      .count();
    
    const rechargeResult = await db.collection('transactions')
      .where({ type: 'recharge', status: 'completed' })
      .field({ amount: true })
      .get();
    
    const withdrawResult = await db.collection('transactions')
      .where({ type: 'withdraw', status: 'completed' })
      .field({ amount: true })
      .get();
    
    const totalRecharge = rechargeResult.data.reduce((sum, t) => sum + t.amount, 0);
    const totalWithdraw = withdrawResult.data.reduce((sum, t) => sum + t.amount, 0);
    
    return {
      success: true,
      data: {
        totalTransactions: totalTransactions.total,
        pendingWithdraws: pendingWithdraws.total,
        totalRecharge,
        totalWithdraw,
        balance: totalRecharge - totalWithdraw
      }
    };
  } catch (error) {
    console.error('获取钱包统计失败:', error);
    return {
      success: false,
      error: '获取钱包统计失败'
    };
  }
}

// 获取通知列表
async function handleGetNotificationList(data) {
  try {
    const { page = 1, limit = 20, type, search } = data || {};
    
    let query = db.collection('notifications');
    
    if (type && type !== 'all') {
      query = query.where({ type });
    }
    
    if (search) {
      query = query.where({
        title: db.RegExp({
          regexp: search,
          options: 'i'
        })
      });
    }
    
    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('createdAt', 'desc')
      .get();
    
    const total = await query.count();
    
    return {
      success: true,
      data: {
        notifications: result.data,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取通知列表失败:', error);
    return {
      success: false,
      error: '获取通知列表失败'
    };
  }
}

// 创建通知
async function handleCreateNotification(data) {
  try {
    const { title, content, type, isBroadcast, userId } = data;
    
    const notification = {
      title,
      content,
      type,
      userId: isBroadcast ? null : userId,
      isRead: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await db.collection('notifications').add({
      data: notification
    });
    
    return {
      success: true,
      data: {
        id: result._id,
        ...notification
      },
      message: '通知创建成功'
    };
  } catch (error) {
    console.error('创建通知失败:', error);
    return {
      success: false,
      error: '创建通知失败'
    };
  }
}

// 删除通知
async function handleDeleteNotification(data) {
  try {
    const { notificationId } = data;
    
    await db.collection('notifications').doc(notificationId).remove();
    
    return {
      success: true,
      message: '通知删除成功'
    };
  } catch (error) {
    console.error('删除通知失败:', error);
    return {
      success: false,
      error: '删除通知失败'
    };
  }
}

// 获取聊天室列表
async function handleGetChatRoomList(data) {
  try {
    const { page = 1, limit = 100, status, search } = data || {};
    
    let query = db.collection('chatRooms');
    
    if (status && status !== 'all' && status !== 'active') {
      query = query.where({ status });
    }
    
    if (search) {
      // 搜索订单标题或订单号
      query = query.where({
        $or: [
          {
            'orderInfo.title': db.RegExp({
              regexp: search,
              options: 'i'
            })
          },
          {
            orderNo: db.RegExp({
              regexp: search,
              options: 'i'
            })
          }
        ]
      });
    }
    
    const result = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy('updateTime', 'desc')
      .get();
    
    const total = await query.count();
    
    console.log('聊天室查询结果:', {
      count: result.data.length,
      total: total.total,
      sample: result.data[0]
    });
    
    return {
      success: true,
      data: {
        chatRooms: result.data,
        total: total.total,
        page,
        limit
      }
    };
  } catch (error) {
    console.error('获取聊天室列表失败:', error);

    // 返回错误信息，不再使用模拟数据
    return {
      success: false,
      error: '获取聊天室列表失败: ' + error.message
    };
  }
}

// 获取聊天消息（管理员版本，无需用户认证）
async function handleGetChatMessages(data) {
  try {
    const { roomId, page = 1, limit = 50 } = data;
    
    console.log('🔍 [管理员API] 获取聊天消息:', { roomId, page, limit });
    
    if (!roomId) {
      console.error('❌ [管理员API] roomId 参数缺失');
      return {
        success: false,
        error: 'roomId 参数缺失'
      };
    }
    
    // 直接查询 messages 表，无需用户认证
    console.log('📥 [管理员API] 开始查询 messages 表...');
    console.log('🔍 [管理员API] 查询条件:', { chatRoomId: roomId });
    
    // 先获取总数
    const countResult = await db.collection('messages')
      .where({ chatRoomId: roomId })
      .count();
    
    console.log('📊 [管理员API] 该聊天室消息总数:', countResult.total);
    
    // 同时尝试模糊查询，以防字段格式问题
    const fuzzyCountResult = await db.collection('messages')
      .where({
        chatRoomId: db.RegExp({
          regexp: roomId,
          options: 'i'
        })
      })
      .count();
    
    console.log('📊 [管理员API] 模糊查询消息总数:', fuzzyCountResult.total);
    
    // 获取所有消息（无限制，确保获取完整记录）
    let allMessages = [];
    let hasMore = true;
    let skip = 0;
    const batchSize = 100; // 每批获取100条
    
    while (hasMore) {
      const batchResult = await db.collection('messages')
        .where({ chatRoomId: roomId })
        .orderBy('createTime', 'asc')
        .skip(skip)
        .limit(batchSize)
        .get();
      
      if (batchResult.data.length > 0) {
        allMessages = allMessages.concat(batchResult.data);
        skip += batchSize;
        console.log(`📊 [管理员API] 已获取 ${allMessages.length} 条消息，继续获取...`);
      } else {
        hasMore = false;
      }
      
      // 安全限制：最多获取10000条消息，防止无限循环
      if (allMessages.length >= 10000) {
        console.log('⚠️ [管理员API] 已达到安全限制10000条消息');
        hasMore = false;
      }
    }
    
    console.log('📊 [管理员API] 最终获取到的消息数量:', allMessages.length);
    
    // 创建结果对象
    const result = { data: allMessages };
    
    console.log('📊 [管理员API] 查询结果:', {
      count: result.data.length,
      roomId: roomId,
      firstMessage: result.data[0] ? {
        id: result.data[0]._id,
        content: result.data[0].content,
        sender: result.data[0].senderInfo?.nickName
      } : '无数据'
    });
    
    // 转换数据格式
    const transformedMessages = result.data.map(msg => {
      const transformed = {
        id: msg._id,
        roomId: msg.chatRoomId,
        senderId: msg.senderId,
        content: msg.content,
        type: msg.type || 'text',
        timestamp: msg.createTime,
        isRead: true,
        senderInfo: msg.senderInfo || {
          nickName: '未知用户',
          avatarUrl: '/placeholder.svg?height=32&width=32'
        }
      };
      
      console.log('🔄 [管理员API] 转换消息:', {
        id: transformed.id,
        content: transformed.content,
        sender: transformed.senderInfo.nickName
      });
      
      return transformed;
    });
    
    console.log('✅ [管理员API] 成功转换消息数量:', transformedMessages.length);
    
    return {
      success: true,
      data: {
        messages: transformedMessages,
        total: transformedMessages.length,
        page: page,
        hasMore: transformedMessages.length === limit
      }
    };
    
  } catch (error) {
    console.error('❌ [管理员API] 获取聊天消息失败:', error);
    console.error('❌ [管理员API] 错误详情:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });
    
    return {
      success: false,
      error: `获取聊天消息失败: ${error.message}`,
      details: {
        code: error.code,
        message: error.message
      }
    };
  }
}

// 禁言用户
async function handleBanUser(data) {
  try {
    const { userId } = data;
    
    await db.collection('users').doc(userId).update({
      isBanned: true,
      bannedAt: new Date(),
      updatedAt: new Date()
    });
    
    return {
      success: true,
      message: '用户已被禁言'
    };
  } catch (error) {
    console.error('禁言用户失败:', error);
    return {
      success: false,
      error: '禁言用户失败'
    };
  }
}

// 获取聊天统计
async function handleGetChatStats() {
  try {
    const totalRooms = await db.collection('chatRooms').count();
    const activeRooms = await db.collection('chatRooms').where({ status: 'active' }).count();
    const totalMessages = await db.collection('chatMessages').count();
    
    return {
      success: true,
      data: {
        totalRooms: totalRooms.total,
        activeRooms: activeRooms.total,
        totalMessages: totalMessages.total
      }
    };
  } catch (error) {
    console.error('获取聊天统计失败:', error);
    return {
      success: false,
      error: '获取聊天统计失败'
    };
  }
}
