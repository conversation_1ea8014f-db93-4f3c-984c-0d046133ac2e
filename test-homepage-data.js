// 测试首页数据流的完整脚本
// 在微信开发者工具的控制台中运行此脚本

console.log('🧪 开始测试首页数据流...');

// 1. 测试 getGrabOrderList API
async function testGetGrabOrderListAPI() {
  console.log('📡 [测试] 调用 getGrabOrderList API...');
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'getGrabOrderList',
      data: {
        page: 1,
        pageSize: 3
      }
    });
    
    console.log('📡 [测试] API 返回结果:', result);
    
    if (result.result && result.result.success) {
      const orders = result.result.data.list;
      console.log('📡 [测试] 订单数量:', orders.length);
      
      orders.forEach((order, index) => {
        console.log(`📡 [测试] 订单 ${index + 1}:`, {
          _id: order._id,
          title: order.title,
          customerId: order.customerId,
          customerInfo: order.customerInfo,
          hasCustomerInfo: !!order.customerInfo,
          customerNickName: order.customerInfo?.nickName,
          customerOpenid: order.customerInfo?.openid
        });
      });
      
      return orders;
    } else {
      console.error('❌ [测试] API 调用失败:', result.result);
      return [];
    }
  } catch (error) {
    console.error('❌ [测试] API 调用异常:', error);
    return [];
  }
}

// 2. 测试 formatOrderData 方法
function testFormatOrderData(order) {
  console.log('🔍 [测试] 开始格式化订单数据...');
  console.log('🔍 [测试] 输入订单数据:', order);
  
  // 模拟首页的 formatOrderData 方法逻辑
  const finalOrder = {
    ...order
  };
  
  const result = {
    ...finalOrder,
    // 确保客户信息存在，保留完整的用户信息
    customerInfo: finalOrder.customerInfo || { 
      _id: finalOrder.customerId,
      nickName: '未知用户', 
      avatarUrl: '',
      openid: finalOrder.customerOpenid || ''
    },
    // 模拟权限判断
    isOwner: false // 假设不是发布者
  };
  
  console.log('🔍 [测试] 格式化结果:', result);
  console.log('🔍 [测试] 最终customerInfo:', result.customerInfo);
  
  return result;
}

// 3. 测试完整的数据流
async function testCompleteDataFlow() {
  console.log('🎯 [测试] 开始完整数据流测试...');
  
  // 步骤1: 获取API数据
  const apiOrders = await testGetGrabOrderListAPI();
  
  if (apiOrders.length === 0) {
    console.log('⚠️ [测试] 没有获取到订单数据，跳过后续测试');
    return;
  }
  
  // 步骤2: 格式化数据
  const formattedOrders = apiOrders.map(order => testFormatOrderData(order));
  
  // 步骤3: 检查结果
  console.log('🎯 [测试] 完整数据流测试结果:');
  formattedOrders.forEach((order, index) => {
    const hasValidUserInfo = order.customerInfo && 
                            order.customerInfo.nickName && 
                            order.customerInfo.nickName !== '未知用户';
    
    console.log(`🎯 [测试] 订单 ${index + 1} 用户信息状态:`, {
      orderId: order._id,
      hasValidUserInfo: hasValidUserInfo,
      nickName: order.customerInfo?.nickName,
      hasOpenid: !!order.customerInfo?.openid
    });
    
    if (!hasValidUserInfo) {
      console.warn(`⚠️ [测试] 订单 ${index + 1} 用户信息不完整!`);
    }
  });
  
  return formattedOrders;
}

// 4. 测试用户信息查询
async function testUserInfoQuery(userId) {
  console.log('👤 [测试] 查询用户信息:', userId);
  
  try {
    const db = wx.cloud.database();
    const result = await db.collection('users')
      .where({ _id: userId })
      .field({ _id: true, nickName: true, avatarUrl: true, openid: true })
      .get();
    
    console.log('👤 [测试] 用户查询结果:', result);
    
    if (result.data.length > 0) {
      const user = result.data[0];
      console.log('👤 [测试] 用户信息:', {
        _id: user._id,
        nickName: user.nickName,
        hasAvatarUrl: !!user.avatarUrl,
        hasOpenid: !!user.openid
      });
      return user;
    } else {
      console.log('❌ [测试] 未找到用户信息');
      return null;
    }
  } catch (error) {
    console.error('❌ [测试] 用户查询失败:', error);
    return null;
  }
}

// 执行测试
console.log('🚀 [测试] 开始执行测试...');

testCompleteDataFlow().then(result => {
  console.log('✅ [测试] 测试完成!');
  
  if (result && result.length > 0) {
    console.log('📋 [测试总结]');
    console.log('1. API 数据获取: ✅');
    console.log('2. 数据格式化: ✅');
    
    const validUserInfoCount = result.filter(order => 
      order.customerInfo && 
      order.customerInfo.nickName && 
      order.customerInfo.nickName !== '未知用户'
    ).length;
    
    console.log(`3. 用户信息完整性: ${validUserInfoCount}/${result.length} 个订单有效`);
    
    if (validUserInfoCount < result.length) {
      console.log('🔧 [建议] 部分订单用户信息不完整，建议检查:');
      console.log('   - getGrabOrderList API 是否正确返回用户信息');
      console.log('   - formatOrderData 是否正确处理用户信息');
      console.log('   - enrichOrdersDataSilently 是否意外覆盖了用户信息');
    }
  }
}).catch(error => {
  console.error('❌ [测试] 测试失败:', error);
});

// 提供手动测试用户查询的函数
window.testUserInfo = testUserInfoQuery;
console.log('💡 [提示] 可以使用 testUserInfo("用户ID") 来手动测试用户信息查询');
