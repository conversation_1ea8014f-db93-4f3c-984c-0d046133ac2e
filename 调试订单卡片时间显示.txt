// 🔍 调试订单卡片时间显示问题的脚本
console.log('🔍 开始调试订单卡片时间显示问题...');

// 1. 获取当前页面实例
const currentPage = getCurrentPages()[getCurrentPages().length - 1];
console.log('📍 当前页面:', currentPage.route);

if (currentPage.route === 'pages/index/index') {
  // 2. 检查页面数据
  const orders = currentPage.data.latestOrders || [];
  console.log(`\n📋 当前页面有${orders.length}个订单:`);
  
  orders.forEach((order, index) => {
    console.log(`\n📦 订单${index + 1}:`, {
      id: order._id?.substring(0, 8) + '...',
      createTime: order.createTime,
      createTimeType: typeof order.createTime,
      isDate: order.createTime instanceof Date,
      isEmptyObject: typeof order.createTime === 'object' && order.createTime !== null && Object.keys(order.createTime).length === 0,
      createTimeText: order.createTimeText
    });
    
    // 测试首页的formatTime方法
    if (currentPage.formatTime) {
      const formattedTime = currentPage.formatTime(order.createTime);
      console.log(`  首页formatTime结果: ${formattedTime}`);
    }
  });
  
  // 3. 检查订单卡片组件
  console.log('\n🎯 检查订单卡片组件:');
  const orderCardComponents = currentPage.selectAllComponents('.order-card');
  console.log(`找到${orderCardComponents.length}个订单卡片组件`);
  
  orderCardComponents.forEach((component, index) => {
    const componentData = component.data;
    console.log(`\n🎴 订单卡片${index + 1}:`, {
      hasOrderData: !!componentData.orderData,
      orderDataId: componentData.orderData?._id?.substring(0, 8) + '...',
      orderDataCreateTime: componentData.orderData?.createTime,
      orderDataCreateTimeType: typeof componentData.orderData?.createTime,
      formattedCreateTime: componentData.formattedCreateTime,
      isDataReady: componentData.isDataReady
    });
    
    // 测试组件的formatTime方法
    if (component.formatTime && componentData.orderData?.createTime) {
      try {
        const componentFormattedTime = component.formatTime(componentData.orderData.createTime);
        console.log(`  组件formatTime结果: ${componentFormattedTime}`);
      } catch (error) {
        console.error(`  组件formatTime错误:`, error);
      }
    }
    
    // 手动调用updateFormattedTime方法
    if (component.updateFormattedTime) {
      console.log(`  手动调用updateFormattedTime...`);
      try {
        component.updateFormattedTime();
        console.log(`  调用后formattedCreateTime: ${component.data.formattedCreateTime}`);
      } catch (error) {
        console.error(`  updateFormattedTime错误:`, error);
      }
    }
  });
  
  // 4. 实时监控组件数据变化
  console.log('\n⏱️ 开始5秒实时监控组件数据变化:');
  let monitorCount = 0;
  const componentMonitor = setInterval(() => {
    monitorCount++;
    console.log(`\n🔍 第${monitorCount}次检查:`);
    
    const currentComponents = getCurrentPages()[getCurrentPages().length - 1].selectAllComponents('.order-card');
    currentComponents.forEach((component, index) => {
      const data = component.data;
      const timeDisplay = data.formattedCreateTime;
      
      if (timeDisplay === '时间未知' || timeDisplay === '未知') {
        console.error(`❌ 组件${index + 1}: 显示"${timeDisplay}"`);
        
        // 详细分析这个组件的数据
        console.log(`  详细数据:`, {
          orderData: data.orderData,
          createTime: data.orderData?.createTime,
          createTimeType: typeof data.orderData?.createTime,
          isDataReady: data.isDataReady,
          formattedCreateTime: data.formattedCreateTime
        });
        
        // 尝试手动修复
        if (component.updateFormattedTime) {
          console.log(`  尝试手动修复...`);
          component.updateFormattedTime();
          console.log(`  修复后: ${component.data.formattedCreateTime}`);
        }
      } else {
        console.log(`✅ 组件${index + 1}: ${timeDisplay}`);
      }
    });
    
    if (monitorCount >= 5) {
      clearInterval(componentMonitor);
      console.log('\n🏁 监控结束');
      
      // 5. 最终诊断
      console.log('\n🔬 最终诊断:');
      console.log('如果仍然看到"时间未知"，可能的原因:');
      console.log('1. 组件数据中的createTime字段为null或undefined');
      console.log('2. 组件数据中的createTime字段为空对象{}');
      console.log('3. 组件的updateFormattedTime方法仍有问题');
      console.log('4. 数据传递过程中时间字段被破坏');
      
      // 提供手动修复方案
      console.log('\n🛠️ 手动修复方案:');
      console.log('如果问题持续，请在控制台执行以下代码:');
      console.log(`
// 强制修复所有订单卡片的时间显示
const components = getCurrentPages()[getCurrentPages().length - 1].selectAllComponents('.order-card');
components.forEach((component, index) => {
  const orderData = component.data.orderData;
  if (orderData && orderData._id) {
    // 强制设置为"刚刚"
    component.setData({
      formattedCreateTime: '刚刚'
    });
    console.log(\`强制修复组件\${index + 1}: 设置为"刚刚"\`);
  }
});
      `);
    }
  }, 1000);
  
} else {
  console.log('⚠️ 当前不在首页，无法检查订单卡片');
}

console.log('\n✅ 调试脚本启动完成！');
